@use '../../utils' as *;

/*----------------------------------------*/
/*  How operate Styles 
/*----------------------------------------*/
.td-how-operate-section {
    background-color: #E6EFFC;

    @include dark-theme {
        background-color: #0C142B;
    }
}

.how-operate-inner {
    position: relative;
    padding: 2px;
    display: block;

    .operate-clip-path-inner {
        display: inline-flex;
        position: relative;
        z-index: 3;
        width: 100%;
        padding-top: 300px;
        padding-bottom: 75px;

        @media #{$xl} {
            padding-top: 200px;
        }

        @media #{$xs,$sm,$md} {
            padding-top: 60px;
            padding-bottom: 0;
        }

        &::before {
            position: absolute;
            top: 0;
            inset-inline-end: 0;
            content: "";
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -36.7%, rgba(153, 153, 153, 0) 101.24%);
            z-index: -1;
            width: calc(100% - 130px);
            height: 100%;
            clip-path: polygon(0% 46.218%, 0% 46.218%, 0.034% 45.114%, 0.135% 44.054%, 0.297% 43.052%, 0.516% 42.118%, 0.787% 41.265%, 1.107% 40.505%, 1.471% 39.85%, 1.874% 39.313%, 2.312% 38.905%, 2.78% 38.639%, 50% 19.872%, 100% 0%, 100% 100%, 3.352% 100%, 3.352% 100%, 2.808% 99.899%, 2.292% 99.608%, 1.812% 99.141%, 1.372% 98.516%, 0.982% 97.747%, 0.647% 96.851%, 0.374% 95.843%, 0.171% 94.739%, 0.044% 93.555%, 0% 92.308%, 0% 46.218%);

            @media #{$xxl,$xl,$lg} {
                width: calc(100% - 30px);
            }

            @media #{$xs,$sm,$md} {
                display: none;
            }
        }

        &::after {
            position: absolute;
            top: 0;
            inset-inline-end: 0;
            content: "";
            clip-path: polygon(0% 46.218%, 0% 46.218%, 0.034% 45.114%, 0.135% 44.054%, 0.297% 43.052%, 0.516% 42.118%, 0.787% 41.265%, 1.107% 40.505%, 1.471% 39.85%, 1.874% 39.313%, 2.312% 38.905%, 2.78% 38.639%, 50% 19.872%, 100% 0%, 100% 100%, 3.352% 100%, 3.352% 100%, 2.808% 99.899%, 2.292% 99.608%, 1.812% 99.141%, 1.372% 98.516%, 0.982% 97.747%, 0.647% 96.851%, 0.374% 95.843%, 0.171% 94.739%, 0.044% 93.555%, 0% 92.308%, 0% 46.218%);
            z-index: -1;
            width: calc(100% - 130px);
            height: 100%;
            background: #F3F7FE;

            @include dark-theme {
                background: var(--td-void);
            }

            @media #{$xxl,$xl,$lg} {
                width: calc(100% - 30px);
            }

            @media #{$xs,$sm,$md} {
                display: none;
            }
        }
    }

    &::before {
        position: absolute;
        inset-inline-end: 2px;
        top: 0;
        transition: all 0.3s;
        width: calc(100% - 132px);
        height: 100%;
        background: #729CFF;
        content: "";
        clip-path: polygon(0% 46.218%, 0% 46.218%, 0.034% 45.114%, 0.135% 44.054%, 0.297% 43.052%, 0.516% 42.118%, 0.787% 41.265%, 1.107% 40.505%, 1.471% 39.85%, 1.874% 39.313%, 2.312% 38.905%, 2.78% 38.639%, 50% 19.872%, 100% 0%, 100% 100%, 3.352% 100%, 3.352% 100%, 2.808% 99.899%, 2.292% 99.608%, 1.812% 99.141%, 1.372% 98.516%, 0.982% 97.747%, 0.647% 96.851%, 0.374% 95.843%, 0.171% 94.739%, 0.044% 93.555%, 0% 92.308%, 0% 46.218%);

        @media #{$xxl,$xl,$lg} {
            width: calc(100% - 30px);
        }

        @media #{$xs,$sm,$md} {
            display: none;
        }
    }
}

.how-operate-item {
    .contents {
        .count {
            display: flex;
            width: 40px;
            height: 40px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 14px;
            border: 1px solid #4776E6;
            background: linear-gradient(90deg, rgba(71, 118, 230, 0.06) 0%, rgba(142, 84, 233, 0.06) 100%);
            color: #222223;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 25px;

            @include dark-theme {
                color: rgba($white, $alpha: 0.6);
            }
        }

        .title {
            font-size: 24px;

            @media #{$xs,$lg} {
                font-size: 20px;
            }
        }

        .description {
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            margin-top: 12px;
        }
    }
}