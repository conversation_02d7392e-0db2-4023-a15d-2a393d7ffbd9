<?php

use App\Enums\KYCStatus;
use App\Enums\TicketStatus;
use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Models\Coin;
use App\Models\Currency;
use App\Models\Gateway;
use App\Models\Language;
use App\Models\LevelReferral;
use App\Models\Page;
use App\Models\PageSetting;
use App\Models\Plugin;
use App\Models\Setting;
use App\Models\Theme;
use App\Models\Ticket;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserNavigation;
use App\Services\CurrencyService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

if (! function_exists('isActive')) {
    function isActive($route, $parameter = null, $class = 'active')
    {
        if ($parameter != null && request()->url() === route($route, $parameter)) {
            return $class;
        }

        if ($parameter == null && is_array($route)) {
            foreach ($route as $value) {
                if (Request::routeIs($value)) {
                    return 'show';
                }
            }
        }

        if ($parameter == null && Request::routeIs($route)) {
            return $class;
        }

        return null;
    }
}

if (! function_exists('tnotify')) {
    function tnotify($type, $message)
    {
        session()->flash('tnotify', [
            'type' => $type,
            'message' => $message,
        ]);
    }
}

if (! function_exists('setting')) {
    function setting($key, $section = null, $default = null)
    {
        if (is_null($key)) {
            return new Setting;
        }

        if (is_array($key)) {

            return Setting::set($key[0], $key[1]);
        }

        $value = Setting::getValue($key, $section, $default);

        return is_null($value) ? value($default) : $value;
    }
}

if (! function_exists('oldSetting')) {

    function oldSetting($field, $section)
    {
        return old($field, setting($field, $section));
    }
}

if (! function_exists('settingValue')) {

    function settingValue($field)
    {
        return Setting::get($field);
    }
}

if (! function_exists('getPageSetting')) {

    function getPageSetting($key)
    {
        return PageSetting::where('key', $key)->first()?->value;
    }
}

if (! function_exists('curl_get_file_contents')) {

    function curl_get_file_contents($URL)
    {
        $c = curl_init();
        curl_setopt($c, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($c, CURLOPT_URL, $URL);
        $contents = curl_exec($c);
        curl_close($c);

        if ($contents) {
            return $contents;
        }

        return false;
    }
}

if (! function_exists('getCountries')) {

    function getCountries()
    {
        return json_decode(file_get_contents(resource_path().'/json/CountryCodes.json'), true);
    }
}

if (! function_exists('getCurrency')) {

    function getCurrency($countryName)
    {
        $currencies = json_decode(getJsonData('currency'), true)['fiat'];

        return collect($currencies)->filter(function ($value) use ($countryName) {
            return str_contains($value['text'], $countryName);
        })->value('id', '');
    }
}

if (! function_exists('getJsonData')) {

    function getJsonData($fileName)
    {
        return file_get_contents(resource_path().sprintf('/json/%s.json', $fileName));
    }
}

if (! function_exists('getTimezone')) {
    function getTimezone()
    {
        $timeZones = json_decode(file_get_contents(resource_path().'/json/timeZone.json'), true);

        return array_values(Arr::sort($timeZones, function ($value) {
            return $value['name'];
        }));
    }
}

if (! function_exists('getIpAddress')) {
    function getIpAddress()
    {
        return request()->ip();
    }
}

if (! function_exists('getLocation')) {
    function getLocation()
    {
        $clientIp = request()->ip();

        $ip = $clientIp == '127.0.0.1' ? '**************' : $clientIp;

        $response = json_decode(curl_get_file_contents('http://ip-api.com/json/'.$ip), true);
        if (isset($response['status']) && $response['status'] == 'fail') {

            return new \Illuminate\Support\Fluent([
                'country_code' => 0,
                'name' => 'Unknown',
                'dial_code' => '',
                'ip' => $ip,
            ]);
        }

        $countryCode = $response['countryCode'] ?? null;

        $currentCountry = collect(getCountries())->first(function ($value) use ($countryCode) {
            return $value['code'] == $countryCode;
        });

        $location = [
            'country_code' => data_get($currentCountry, 'code', 0),
            'name' => data_get($currentCountry, 'name', 'Unknown'),
            'dial_code' => data_get($currentCountry, 'dial_code', ''),
            'ip' => $response['query'] ?? $ip,
        ];

        return new \Illuminate\Support\Fluent($location);
    }
}

if (! function_exists('gateway_info')) {
    function gateway_info($code)
    {
        $info = Gateway::where('gateway_code', $code)->first();

        return json_decode($info->credentials);
    }
}

if (! function_exists('plugin_active')) {
    function plugin_active($name)
    {
        return Plugin::where('name', $name)->where('status', true)->first();
    }
}

if (! function_exists('get_navigation_name')) {
    function get_navigation_name($type)
    {
        $navigation = UserNavigation::where('type', $type)->first();

        return $navigation->name ?? '';
    }
}

if (! function_exists('default_plugin')) {
    function default_plugin($type)
    {
        return Plugin::where('type', $type)->where('status', 1)->first('name')?->name;
    }
}

if (! function_exists('br2nl')) {
    function br2nl($input)
    {
        return preg_replace('/<br\\s*?\/??>/i', '', $input);
    }
}

if (! function_exists('safe')) {
    function safe($input)
    {
        if (! env('APP_DEMO', false)) {
            return $input;
        }

        if (filter_var($input, FILTER_VALIDATE_EMAIL)) {

            $emailParts = explode('@', $input);
            $username = $emailParts[0];
            $hiddenUsername = substr($username, 0, 2).str_repeat('*', strlen($username) - 2);
            $hiddenEmailDomain = substr($emailParts[1], 0, 2).str_repeat('*', strlen($emailParts[1]) - 3).$emailParts[1][strlen($emailParts[1]) - 1];

            return $hiddenUsername.'@'.$hiddenEmailDomain;
        }

        return preg_replace('/(\d{3})\d{3}(\d{3})/', '$1****$2', $input);
    }
}

if (! function_exists('site_theme')) {
    function site_theme()
    {
        return once(function () {
            return Theme::where('status', true)->value('name');
        });
    }
}

if (! function_exists('getLandingContents')) {
    function getLandingContents($type)
    {
        $data = \App\Models\LandingContent::where('locale', app()->getLocale())->where('theme', site_theme())->where('type', $type)->get();

        if (! $data->count()) {
            $data = \App\Models\LandingContent::where('locale', defaultLocale())->where('theme', site_theme())->where('type', $type)->get();
        }

        return $data;
    }
}
if (! function_exists('getLandingData')) {
    function getLandingData($type, $fluent = true)
    {
        $data = \App\Models\LandingPage::where('locale', app()->getLocale())->where('theme', site_theme())->where('code', $type)->first();

        if (! $data) {
            $data = \App\Models\LandingPage::where('locale', defaultLocale())->where('theme', site_theme())->where('code', $type)->first();
        }

        return $fluent ? fluent(json_decode($data->data, true)) : json_decode($data->data, true);
    }
}

if (! function_exists('generate_date_range_array')) {
    function generate_date_range_array($startDate, $endDate): array
    {
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        $dates = collect([]);

        while ($startDate->lte($endDate)) {
            $dates->push($startDate->format('d M'));
            $startDate->addDay();
        }

        return $dates->toArray();
    }
}

if (! function_exists('getQRCode')) {
    function getQRCode($data)
    {
        return 'https://api.qrserver.com/v1/create-qr-code/?size=400x400&data='.$data;
    }
}

if (! function_exists('generateAccountNumber')) {
    function generateAccountNumber()
    {
        do {
            $account_number = substr(random_int(****************, ****************), 0, setting('account_no_limit', 'global'));
        } while (User::where('account_number', $account_number)->exists());

        return $account_number;
    }
}

if (! function_exists('generateReferralCode')) {
    function generateReferralCode()
    {
        do {
            $referral_code = Str::random(setting('referral_code_limit', 'global'));
        } while (User::where('referral_code', $referral_code)->exists());

        return $referral_code;
    }
}

if (! function_exists('generateUniqueUsername')) {
    function generateUniqueUsername($name)
    {
        do {
            $username = str_replace(' ', '', strtolower($name)).rand(1000, 9999);
        } while (User::where('username', $username)->exists());

        return $username;
    }
}

if (! function_exists('pending_count')) {
    function pending_count()
    {
        $withdrawCount = Transaction::where('type', TxnType::Withdraw)
            ->where('status', TxnStatus::Pending)
            ->count();

        $kycCount = User::where('kyc', KYCStatus::Pending)->count();

        $depositCount = Transaction::where('type', TxnType::ManualDeposit)
            ->where('status', TxnStatus::Pending)
            ->count();

        $ticketCount = Ticket::where('status', TicketStatus::OPEN)->count();

        return [
            'withdraw_count' => $withdrawCount,
            'kyc_count' => $kycCount,
            'deposit_count' => $depositCount,
            'ticket_count' => $ticketCount,
        ];
    }
}

if (! function_exists('defaultLocale')) {
    function defaultLocale()
    {
        $language = Language::where('is_default', true)->first();

        return $language->locale ?? 'en';
    }
}

if (! function_exists('isRtl')) {
    function isRtl($code)
    {
        return once(function () use ($code) {
            return Language::where('locale', $code)->first()->is_rtl ?? false;
        });
    }
}

if (! function_exists('getActiveLangName')) {
    function getActiveLangName()
    {
        return Language::where('locale', app()->getLocale())->first()?->name;
    }
}

if (! function_exists('getBrowser')) {

    function getBrowser($user_agent = null)
    {

        $user_agent = $user_agent != null ? $user_agent : request()->userAgent();

        $browser = 'Unknown';
        $platform = 'Unknown';

        if (preg_match('/linux/i', $user_agent)) {
            $platform = 'Linux';
        } elseif (preg_match('/macintosh|mac os x/i', $user_agent)) {
            $platform = 'Mac';
        } elseif (preg_match('/windows|win32/i', $user_agent)) {
            $platform = 'Windows';
        } elseif (preg_match('/windows|win32/i', $user_agent)) {
            $platform = 'Windows';
        }

        if (preg_match('/MSIE/i', $user_agent) && ! preg_match('/Opera/i', $user_agent)) {
            $browser = 'IE';
        } elseif (preg_match('/Firefox/i', $user_agent)) {
            $browser = 'Firefox';
        } elseif (preg_match('/OPR/i', $user_agent)) {
            $browser = 'Opera';
        } elseif (preg_match('/Chrome/i', $user_agent) && ! preg_match('/Edge/i', $user_agent)) {
            $browser = 'Chrome';
        } elseif (preg_match('/Safari/i', $user_agent) && ! preg_match('/Edge/i', $user_agent)) {
            $browser = 'Safari';
        } elseif (preg_match('/Netscape/i', $user_agent)) {
            $browser = 'Netscape';
        } elseif (preg_match('/Edge/i', $user_agent)) {
            $browser = 'Edge';
        } elseif (preg_match('/Trident/i', $user_agent)) {
            $browser = 'IE';
        }

        return [
            'browser' => $browser,
            'platform' => $platform,
        ];
    }
}

if (! function_exists('mySqlVersion')) {
    function mySqlVersion()
    {
        $pdo = DB::connection()->getPdo();
        $version = $pdo->query('select version()')->fetchColumn();

        preg_match("/^[0-9\.]+/", $version, $match);

        return $match[0];
    }
}

if (! function_exists('notify')) {
    function notify(?string $message = null, ?string $title = null)
    {
        $notify = app('notify');

        if (! is_null($message)) {
            return $notify->success($message, $title);
        }

        return $notify;
    }
}

if (! function_exists('hideCharacter')) {
    function hideCharacter($string)
    {
        // Get the last 4 digits of the account number
        $lastFourDigits = substr($string, -4);

        // Mask the rest of the account number with asterisks
        $masked = str_repeat('*', strlen($string) - 4).$lastFourDigits;

        return $masked;
    }
}

if (! function_exists('months')) {
    function months()
    {

        $start = Carbon::createFromDate(null, 1, 1); // Set January
        $months = [];

        for ($i = 0; $i < 12; $i++) {
            $months[] = $start->monthName;
            $start->addMonth();
        }

        return $months;
    }
}

if (! function_exists('days')) {
    function days()
    {

        $days = [];
        $start = Carbon::now();

        for ($i = 0; $i < 32; $i++) {
            $days[] = $start->copy()->subDays($i)->day; // Format as 'Y-m-d'
        }

        return $days;
    }
}

if (! function_exists('socialMediaShareLinks')) {
    function socialMediaShareLinks(string $path, string $provider)
    {
        switch ($provider) {
            case 'facebook':
                $share_link = 'https://www.facebook.com/sharer/sharer.php?u='.$path;
                break;
            case 'twitter':
                $share_link = 'https://twitter.com/intent/tweet?text='.$path;
                break;
            case 'linkedin':
                $share_link = 'https://www.linkedin.com/shareArticle?mini=true&url='.$path;
                break;
        }

        return $share_link;
    }
}

if (! function_exists('formatNumber')) {
    function formatNumber($number)
    {
        if ($number >= 1000000) {
            return number_format($number / 1000000, 1).'M+';
        } elseif ($number >= 1000) {
            return number_format($number / 1000, 1).'K+';
        }

        return $number;
    }
}

if (! function_exists('creditReferralBonus')) {
    function creditReferralBonus($user, $type, $mainAmount, $level = null, $depth = 1, $fromUser = null, $wallet_id = 'default')
    {
        $LevelReferral = LevelReferral::query()
            ->where('type', $type)
            ->where('the_order', $depth)
            ->first('bounty');

        if ($user->ref_id !== null && $depth <= $level && $LevelReferral) {
            $referrer = User::find($user->ref_id);

            $bounty = $LevelReferral->bounty;

            $amount = (float) ($mainAmount * $bounty) / 100;

            $fromUserReferral = $fromUser == null ? $user : $fromUser;

            $description = ucwords(str_replace('_', ' ', $type)).' Referral Bonus Via '.$fromUserReferral->full_name.' - Level '.$depth;

            $transaction = new Transaction;
            $transaction->user_id = $referrer->id;
            $transaction->from_user_id = $fromUserReferral->id;
            $transaction->from_model = 'User';
            $transaction->description = $description;
            $transaction->amount = $amount;
            $transaction->type = TxnType::Referral;
            $transaction->charge = 0;
            $transaction->final_amount = $amount;
            $transaction->method = 'System';
            $transaction->status = TxnStatus::Success;
            $transaction->wallet_type = $wallet_id;
            $transaction->save();

            $referrer->balance += $amount;
            $referrer->save();

            creditReferralBonus($referrer, $type, $mainAmount, $level, $depth + 1, $user);
        }
    }
}

if (! function_exists('creditCurrencyWiseReferralBonus')) {
    function creditCurrencyWiseReferralBonus($user, $type, $mainAmount, $level = null, $depth = 1, $fromUser = null, $userWallet = null)
    {
        $level = LevelReferral::where('type', $type)->max('the_order') + 1;

        // Convert the amount to the site currency
        $toCurrency = setting('site_currency', 'global');

        if ($userWallet?->currency instanceof Currency) {
            $mainAmount = formatAmount(CurrencyService::convert($mainAmount, $userWallet->currency->code, $toCurrency), $toCurrency, false, true);
        }

        creditReferralBonus($user, $type, $mainAmount, $level, $depth, $fromUser, 'default');
    }
}

if (! function_exists('getShortName')) {
    function getShortName(?string $name): string
    {
        try {
            return collect(explode(' ', $name))
                ->filter()
                ->map(function ($part) {
                    return strtoupper($part[0]);
                })
                ->implode('');
        } catch (Exception $exception) {
            return 'N/A';
        }
    }
}

if (! function_exists('isPlusTransaction')) {
    function isPlusTransaction($type)
    {
        return in_array((string) $type, [
            TxnType::Deposit->value,
            TxnType::ManualDeposit->value,
            TxnType::Refund->value,
            TxnType::Referral->value,
            TxnType::SignupBonus->value,
            TxnType::Credit->value,
            TxnType::BoostedReferral->value,
            TxnType::BoostedMiningReturn->value,
            TxnType::MiningReturn->value,
            TxnType::PortfolioBonus->value,
        ]);
    }
}

if (! function_exists('formatAmount')) {
    function formatAmount($amount, $currency = null, $showCurrency = false, $precision = 2): string
    {
        // if currency is string and not instance of Currency, then get the currency from cache
        if (is_string($currency) && ! $currency instanceof Coin) {
            $currency = once(fn () => Coin::where('code', $currency)->first());
        }
        // Set the precision
        $currencyCode = $currency instanceof Coin ? $currency->code : setting('site_currency');
        $amount = $currencyCode == setting('site_currency') ? number_format($amount, setting('site_currency_decimals', 'global')) : number_format($amount, 8);

        return $showCurrency ? sprintf('%s %s', $amount, $currencyCode) : $amount;
    }
}

if (! function_exists('currency')) {
    function currency()
    {
        return new CurrencyService;
    }
}

if (! function_exists('notificationIcon')) {
    function notificationIcon($type, $for = 'user')
    {
        // User Icons
        $userIcons = [
            'user_mail' => 'hugeicons--invoice-01',
            'user_manual_deposit_approved' => 'hugeicons--money-bag-02',
            'user_manual_deposit_rejected' => 'hugeicons--money-bag-02',
            'user_invoice_payment' => 'hugeicons--invoice-01',
            'user_request_money' => 'hugeicons--money-bag-02',
            'user_gift_redeem' => 'hugeicons--gift',
            'user_cashout' => 'hugeicons--wallet-03',
            'user_withdraw' => 'hugeicons--reverse-withdrawal-01',
            'user_transfer_money' => 'mynaui--send-solid',
            'user_referral_join' => 'hugeicons--workflow-square-10',
            'user_ticket_reply' => 'hugeicons--customer-support',
        ];

        return match ($for) {
            'user' => $userIcons[$type] ?? 'hugeicons--notification-02',
            default => 'hugeicons--notification-02',
        };
    }
}

if (! function_exists('getTransactionIcon')) {
    function getTransactionIcon(string $type): string
    {
        $icons = [
            'Credit' => 'tabler:credit-card',
            'Debit' => 'tabler:shopping-cart',
            'Deposit' => 'tabler:cash',
            'Manual_Deposit' => 'tabler:wallet',
            'Withdraw' => 'tabler:arrow-up-circle',
            'Referral' => 'tabler:user-plus',
            'Withdraw_Auto' => 'tabler:arrow-up-circle',
            'Refund' => 'tabler:arrow-back-up',
            'Signup_Bonus' => 'tabler:gift',
            'Payment' => 'tabler:currency-dollar',
            'Mining_Return' => 'tabler:chart-line',
            'Boosted_Referral' => 'tabler:rocket',
            'Boosted_Mining_Return' => 'tabler:bolt',
            'Portfolio_Bonus' => 'tabler:briefcase',
        ];

        return $icons[$type] ?? 'tabler:cash';
    }
}
if (! function_exists('highlightColor')) {
    function highlightColor($text, $class = 'highlight')
    {
        return preg_replace_callback('/\[\[color_text=(.*?)\]\]/', function ($matches) use ($class) {
            return '<span class="'.$class.'">'.$matches[1].'</span>';
        }, $text);
    }
}

if (! function_exists('greeting')) {
    function greeting(): string
    {
        $hour = now()->hour;

        if ($hour < 12) {
            $greeting = __('Good morning');
        } elseif ($hour < 18) {
            $greeting = __('Good afternoon');
        } else {
            $greeting = __('Good evening');
        }

        return $greeting;
    }
}

if (! function_exists('getBasename')) {
    function getBasename($path): string
    {
        return basename($path);
    }
}

function frontendAsset($path)
{
    return asset('frontend/'.site_theme().'/'.$path);
}
function getThemeMode()
{
    return session('theme_mode', setting('default_mode', 'global')) ?? setting('default_mode', 'global');
}

function isDarkMode()
{
    return getThemeMode() == 'dark';
}

function getThemeBodyClass()
{
    return getThemeMode() == 'dark' ? 'dark-theme' : '';
}
function cryptoFormat($amount, $decimals = 8)
{
    return number_format($amount, $decimals);
}

function getPageData($page)
{
    $data = Page::where('code', $page)->where('locale', app()->getLocale())->where('theme', site_theme())->first();
    if (! $data) {
        $data = Page::where('code', $page)->where('locale', defaultLocale())->where('theme', site_theme())->first();
    }

    return $data;
}

function trxAmountFormat($transaction, $col)
{
    return formatAmount($transaction->{$col}, $transaction->wallet_type == 'default' ? setting('site_currency', section: 'global') : $transaction->pay_currency, true);
}

function isDevMode()
{
    $request = request();
    return config('app.env') == 'local' || config('app.debug') || config('debugbar.enabled') || 
    in_array($request->ip(), ['127.0.0.1', 'localhost', '::1', '***********']) ||
    in_array($request->server('HTTP_X_FORWARDED_FOR'), ['127.0.0.1', 'localhost', '::1', '***********']) ||
    in_array($request->server('HTTP_CLIENT_IP'), ['127.0.0.1', 'localhost', '::1', '***********'])
    || in_array($request->server('REMOTE_ADDR'), ['127.0.0.1', 'localhost', '::1', '***********'])
    || in_array($request->server('HTTP_X_REAL_IP'), ['127.0.0.1', 'localhost', '::1', '***********']) ||
    str($request->server('SERVER_NAME'))->contains(['localhost', '127.0.0.1', '::1', '***********','orexcoin','tdevs.co'])
    ;
}