@use '../utils/variables' as *;

:root {
  // font family declaration
  --td-ff-body: "Outfit", sans-serif;
  --td-ff-heading: "Bai Jamjuree", sans-serif;
  --td-ff-fontawesome: "Font Awesome 6 Pro";

  // color declaration

  // Common Color
  --td-white: #ffffff;
  --td-black: #000000;
  --td-placeholder: hsla(0, 0%, 0%, 0.5);
  --td-selection: hsl(0, 0%, 0%);

  // Body Color
  --td-body: #010C1A;

  // Heading Color
  --td-heading: #222223;

  // Theme Color
  --td-primary: #FB405A;
  --td-deep-black: #1A1D1F;

  // Text Color
  --td-text-primary: #47494E;

  // Border Color
  --td-border-primary: #eaeaea;

  // Others Color
  --td-warning: #FFA336;
  --td-success: #{$success};
  --td-danger: #FB405A;
  --td-danger-alt: #eb4e5c;
  --td-green: #03A66D;
  --td-info: #2E87E8;

  --td-eerie-black: #091628;
  --td-void: #020A22;

  // Gradients colors
  --td-gradient-1: #C340C0;
  --td-gradient-2: #FB405A;
  --td-gradient-3: #F7A34A;

  //font weight declaration
  --td-fw-normal: normal;
  --td-fw-thin: 100;
  --td-fw-elight: 200;
  --td-fw-light: 300;
  --td-fw-regular: 400;
  --td-fw-medium: 500;
  --td-fw-sbold: 600;
  --td-fw-bold: 700;
  --td-fw-ebold: 800;
  --td-fw-black: 900;

  // font size declaration
  --td-fs-body: 16px;
  --td-fs-p: 16px;
  --td-fs-h1: 52px;
  --td-fs-h2: 42px;
  --td-fs-h3: 32px;
  --td-fs-h4: 24px;
  --td-fs-h5: 20px;
  --td-fs-h6: 16px;
}

:root .dark-theme {
  --td-heading: #fff;
  --td-text-primary: #9A9DA7;
}