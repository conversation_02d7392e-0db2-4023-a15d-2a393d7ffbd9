<!-- Pricing section start -->
<div class="td-pricing-section p-relative zi-11 @if (!Route::is('page')) section_space @endif">
    <div class="container">
       @if (Route::is('home'))
       <div class="row justify-content-center">
          <div class="col-xxl-7 col-xl-7 col-lg-6">
            <div class="section-title-wrapper is-white text-center section_title_space">
               <h2 class="section-title has_text_move_anim mb-15">{!! highlightColor($data['hero_title']) !!}</h2>
               <p class="description has_fade_anim">{{ $data['sub_title'] }}</p>
            </div>
         </div>
      </div>
      @endif
       <div class="row justify-content-center">
          <div class="col-xxl-12">
             @include('frontend::user.mining.include.pricing')
          </div>
       </div>
    </div>
    <div class="pricing-overlay-bg" data-background="{{ frontendAsset('images/bg/pricing-overlay-bg.png') }}"></div>
    <div class="pricing-dot-bg" data-background="{{ frontendAsset('images/bg/pricing-dot.png') }}"></div>
</div>
 <!-- Pricing section end -->