<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Holiday;
use App\Models\Miner;
use App\Models\Schedule;
use App\Models\Scheme;
use App\Traits\ImageUpload;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SchemeController extends Controller implements HasMiddleware
{
    use ImageUpload;

    public static function middleware()
    {
        return [
            new Middleware('permission:schema-list', ['only' => ['index']]),
            new Middleware('permission:schema-create', ['only' => ['create', 'store']]),
            new Middleware('permission:schema-edit', ['only' => ['edit', 'update']]),
            new Middleware('permission:schema-delete', ['only' => ['delete']]),
        ];
    }

    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|View
     */
    public function index()
    {
        $schemas = Scheme::latest()->paginate();

        return view('backend.schema.index', compact('schemas'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Application|Factory|View
     */
    public function create()
    {
        $schedules = Schedule::all();
        $miners = Miner::active()->with('coin:id,code')->get();
        $holidays = Holiday::all();
        $speeds = config('speed');
        $offDaySchedule = [
            'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday',
        ];

        return view('backend.schema.create', compact('schedules', 'miners', 'holidays', 'speeds', 'offDaySchedule'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:schemes,name',
            'miner_id' => 'required|exists:miners,id',
            'price' => 'required|numeric|min:0',
            'icon' => 'required|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'return_amount_type' => 'required|in:fixed,min_max',
            'return_amount_value' => 'required_if:return_amount_type,fixed|numeric|min:0',
            'return_min_amount' => 'required_if:return_amount_type,min_max|numeric|min:0',
            'return_max_amount' => 'required_if:return_amount_type,min_max|numeric|min:0|gt:return_min_amount',
            'return_period_type' => 'required|in:period,lifetime',
            'return_period' => 'required|exists:schedules,id',
            'return_period_max_number' => 'required_if:return_period_type,period|nullable|integer|min:1',
            'speed' => 'required|in:'.implode(',', config('speed')),
            'speed_amount' => 'required|numeric|min:0',
            'max_mining_amount' => 'nullable|numeric|min:0',
            'is_featured' => 'boolean',
            'features' => 'required|array',
            'description' => 'required|string',
            'holidays' => 'nullable|array',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first(), 'Error');

            return redirect()->back()->withErrors($validator)->withInput();
        }

        $input = $request->all();

        $holidays = $request->holidays ?? [];
        DB::beginTransaction();
        try {
            Scheme::create([
                'name' => $input['name'],
                'miner_id' => $input['miner_id'],
                'price' => $input['price'],
                'return_amount_type' => $input['return_amount_type'],
                'return_amount_value' => $input['return_amount_type'] == 'fixed' ? $input['return_amount_value'] : null,
                'return_min_amount' => $input['return_amount_type'] == 'min_max' ? $input['return_min_amount'] : null,
                'return_max_amount' => $input['return_amount_type'] == 'min_max' ? $input['return_max_amount'] : null,
                'return_period_type' => $input['return_period_type'],
                'return_period' => $input['return_period'],
                'return_period_hours' => Schedule::find($input['return_period'])->time,
                'return_period_max_number' => $input['return_period_type'] == 'period' ? $input['return_period_max_number'] : null,
                'speed' => $input['speed'],
                'speed_amount' => $input['speed_amount'],
                'max_mining_amount' => $request->float('max_mining_amount', 0),
                'is_featured' => $input['is_featured'] ?? false,
                'description' => $input['description'] ?? null,
                'holidays' => $holidays,
                'status' => $input['status'],
                'features' => $request->features ?? [],
                'maintenance_fee_amount' => $input['maintenance_fee_amount'] ?? 0,
                'maintenance_fee_type' => $input['maintenance_fee_type'] ?? 'percentage',
                'icon' => self::imageUploadTrait($request->icon, folderPath: 'schema'),

            ]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            notify()->error($e->getMessage(), 'Error');

            return redirect()->back()->withInput();
        }

        notify()->success('Schema created successfully');

        return redirect()->route('admin.schema.index');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Application|Factory|View
     */
    public function edit($id)
    {
        $schema = Scheme::findOrFail($id);
        $schedules = Schedule::all();
        $miners = Miner::active()->with('coin:id,code')->get();
        $holidays = Holiday::all();

        $offDaySchedule = [
            'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday',
        ];
        $speeds = config('speed');

        return view('backend.schema.edit', compact('schema', 'schedules', 'miners', 'holidays', 'speeds', 'offDaySchedule'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:schemes,name,'.$id,
            'miner_id' => 'required|exists:miners,id',
            'price' => 'required|numeric|min:0',
            'return_amount_type' => 'required|in:fixed,min_max',
            'return_amount_value' => 'required_if:return_amount_type,fixed|numeric|min:0',
            'return_min_amount' => 'required_if:return_amount_type,min_max|numeric|min:0',
            'return_max_amount' => 'required_if:return_amount_type,min_max|numeric|min:0|gt:return_min_amount',
            'return_period_type' => 'required|in:period,lifetime',
            'return_period' => 'required|exists:schedules,id',
            'return_period_max_number' => 'required_if:return_period_type,period|nullable|integer|min:1',
            'speed' => 'required|in:'.implode(',', config('speed')),
            'speed_amount' => 'required|numeric|min:0',
            'max_mining_amount' => 'nullable|numeric|min:0',
            'is_featured' => 'boolean',
            'features' => 'required|array',
            'description' => 'required|string',
            'holidays' => 'nullable|array',
            'status' => 'required|in:active,inactive',
            'icon' => 'nullable|mimes:jpeg,png,jpg,gif,svg|max:2048',

        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first(), 'Error');

            return redirect()->back()->withErrors($validator)->withInput();
        }

        $schema = Scheme::findOrFail($id);
        $input = $request->all();

        $holidays = $request->holidays ?? [];
        DB::beginTransaction();
        try {
            $schema->update([
                'name' => $input['name'],
                'miner_id' => $input['miner_id'],
                'price' => $input['price'],
                'return_amount_type' => $input['return_amount_type'],
                'return_amount_value' => $input['return_amount_type'] == 'fixed' ? $input['return_amount_value'] : null,
                'return_min_amount' => $input['return_amount_type'] == 'min_max' ? $input['return_min_amount'] : null,
                'return_max_amount' => $input['return_amount_type'] == 'min_max' ? $input['return_max_amount'] : null,
                'return_period_type' => $input['return_period_type'],
                'return_period' => $input['return_period'],
                'return_period_hours' => Schedule::find($input['return_period'])->time,
                'return_period_max_number' => $input['return_period_type'] == 'period' ? $input['return_period_max_number'] : null,
                'speed' => $input['speed'],
                'speed_amount' => $input['speed_amount'],
                'max_mining_amount' => $request->float('max_mining_amount', 0),
                'is_featured' => $input['is_featured'] ?? false,
                'description' => $input['description'] ?? null,
                'holidays' => $holidays,
                'status' => $input['status'],
                'features' => $request->features ?? [],
                'maintenance_fee_amount' => $input['maintenance_fee_amount'] ?? 0,
                'maintenance_fee_type' => $input['maintenance_fee_type'] ?? 'percentage',
                'icon' => $request->hasFile('icon') ? self::imageUploadTrait($request->icon, folderPath: 'schema') : $schema->icon,
            ]);
            DB::commit();
        } catch (\Throwable $th) {
            // throw $th;
            DB::rollBack();
            notify()->error($th->getMessage(), 'Error');

            return redirect()->back()->withInput();
        }

        notify()->success('Schema updated successfully');

        return redirect()->route('admin.schema.index');
    }

    public function destroy($id)
    {
        $schema = Scheme::withCount(['userMining'])->find($id);
        if ($schema->icon !== null) {
            self::fileDelete($schema->icon);
        }
        if ($schema->user_mining_count > 0) {
            notify()->error('Schema is used in user mining. You can not delete it!');

            return redirect()->back();
        }
        $schema->delete();
        notify()->success('Schema deleted successfully');

        return redirect()->route('admin.schema.index');
    }
}
