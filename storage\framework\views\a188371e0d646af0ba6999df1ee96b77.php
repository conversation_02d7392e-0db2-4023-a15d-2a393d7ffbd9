<div class="col-xxl-12">
    <div class="row gy-30">
        <div class="col-xxl-12">
            <!-- KYC Verification area start -->
            <div class="KYC-verification-are default-area-style">
                <div class="heading-top">
                    <h5 class="title"><?php echo e(__('Verification Center')); ?></h5>
                </div>
                <div class="default-content-inner">
                    <div class="verification-inner-wrapper">
                        <div class="identity-alert-buttons">
                            <?php $__empty_1 = true; $__currentLoopData = $data['kycs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kyc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <a href="<?php echo e(route('user.kyc.submission', [encrypt($kyc->id), 'type' => 'kyc'])); ?>"
                                    class="td-btn btn-chip grd-fill-btn-primary">
                                    <span class="btn-icon">
                                        <iconify-icon icon="tabler:cards"></iconify-icon>
                                    </span>
                                    <span class="btn-text"><?php echo e($kyc->name); ?></span>
                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <p class="mb-0">
                                    <i><?php echo e(__('You have nothing to submit')); ?></i>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <!-- KYC Verification area end -->
        </div>

        <div class="col-xxl-12">
            <!-- KYC history area start -->
            <div class="KYC-verification-are default-area-style">
                <div class="heading-top">
                    <h5 class="title"><?php echo e(__('Verification History')); ?></h5>
                </div>
                <div class="default-content-inner">
                    <div class="verification-inner-wrapper d-flex flex-column gap-3">
                        <?php $__empty_1 = true; $__currentLoopData = $data['user_kycs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user_kyc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                                    'identity-alert',
                                                    'danger' => $user_kyc->status == 'rejected',
                                                    'success' => $user_kyc->status == 'approved',
                                                    'info' => $user_kyc->status == 'pending'
                                                ]); ?>">
                                                    <div class="left-contents">
                                                        <div class="icon">
                                                            <img src="<?php echo e(frontendAsset('images/icons/alert/' . ($user_kyc->status == 'rejected' ? 'danger-close.svg' : ($user_kyc->status == 'approved' ? 'success.svg' : 'info.svg')))); ?>"
                                                                alt="">
                                                        </div>
                                                        <div class="contents">
                                                            <h4 class="title"><?php echo e($user_kyc->kyc->name); ?></h4>
                                                            <div class="content">
                                                                <p><?php echo e(__('Submission date:')); ?>

                                                                    <?php echo e(date('d M Y h:i A', strtotime($user_kyc->created_at))); ?></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="right-actions">
                                                        <span class="td-badge fill-badge-<?php echo e(match ($user_kyc->status) {
                                'approved' => 'success',
                                'rejected' => 'danger',
                                'pending' => 'warning',
                                default => 'primary',
                            }); ?>"><?php echo e(ucfirst($user_kyc->status)); ?></span>
                                                        <a class="td-underline-btn has-grad-one" href="javascript:void(0)" id="openModal"
                                                            data-id="<?php echo e($user_kyc->id); ?>">
                                                            <?php echo e(__('View Details')); ?>

                                                        </a>
                                                    </div>
                                                </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <?php if (isset($component)) { $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.no-data-found','data' => ['class' => 'mt-10','module' => ''.e(__('KYC History')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('no-data-found'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mt-10','module' => ''.e(__('KYC History')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $attributes = $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $component = $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
                        <?php endif; ?>


                    </div>
                </div>
            </div>
            <!-- KYC history area end -->
        </div>

        <!-- Modal placed once -->
        <div class="modal default-model fade m-0" id="kycAlerttModal" tabindex="-1" aria-labelledby="kycAlerttModalLabel"
            aria-modal="true" role="dialog">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="kycAlerttModalLabel"><?php echo e(__('KYC Details')); ?></h1>
                    </div>
                    <div class="modal-body">
                        <div class="modal-wallet-contents modal-list">
                        </div>
                        <div class="modal-buttons mt-3 d-flex gap-3 flex-wrap">
                            <button type="button" class="td-btn btn-chip grd-fill-btn-primary"
                                data-bs-dismiss="modal" aria-label="Close">
                                <span class="inner-btn">
                                    <span class="btn-text"><?php echo e(__('Close')); ?></span>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).on('click', '#openModal', function () {
            "use strict";

            let id = $(this).data('id');

            $.get("<?php echo e(route('user.kyc.details')); ?>", {
                id: id
            }, function (response) {
                $('.modal-wallet-contents').html(response.html);
                $('#kycAlerttModal').modal('show');
            });
        });
    </script>
<?php $__env->stopPush(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/settings/include/__kyc.blade.php ENDPATH**/ ?>