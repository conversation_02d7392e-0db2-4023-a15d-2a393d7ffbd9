<div class="modal fade" id="addNew" tabindex="-1" aria-labelledby="addNewTestimonialModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content site-table-modal">
            <div class="modal-body popup-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">

                </button>
                <div class="popup-body-text">
                    <h3 class="title mb-4"><?php echo e(__('Add New')); ?></h3>
                    <form action="<?php echo e(route('admin.page.testimonial.store')); ?>" method="post"
                        enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>

                        <div class="site-input-groups row">
                            <label for="" class="box-input-label"><?php echo e(__('Thumbnail')); ?></label>
                            <div class="wrap-custom-file">
                                <input type="file" name="picture" id="picture"
                                    accept=".gif, .jpg, .png, .svg, .webp" />
                                <label for="picture">
                                    <img class="upload-icon" src="<?php echo e(asset('global/materials/upload.svg')); ?>"
                                        alt="" />
                                    <span><?php echo e(__('Upload')); ?></span>
                                </label>
                            </div>
                        </div>
                        <div class="site-input-groups">
                            <label for="" class="box-input-label"><?php echo e(__('Name:')); ?></label>
                            <input type="text" name="name" class="box-input mb-0" placeholder="Name"
                                required="" />
                        </div>
                        <div class="site-input-groups">
                            <label for="" class="box-input-label"><?php echo e(__('Designation:')); ?></label>
                            <input type="text" name="designation" class="box-input mb-0" placeholder="Designation"
                                required="" />
                        </div>
                        <div class="site-input-groups">
                            <label for="" class="box-input-label"><?php echo e(__('Message:')); ?></label>
                            <textarea name="message" class="form-textarea mb-0" placeholder="Message"></textarea>
                        </div>

                        <div class="action-btns">
                            <button type="submit" class="site-btn-sm primary-btn me-2">
                                <i data-lucide="check"></i>
                                <?php echo e(__('Add New')); ?>

                            </button>
                            <a href="#" class="site-btn-sm red-btn" data-bs-dismiss="modal" aria-label="Close">
                                <i data-lucide="x"></i>
                                <?php echo e(__('Close')); ?>

                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/page/default/section/include/__add_new_testimonial.blade.php ENDPATH**/ ?>