@extends('frontend::layouts.user')

@section('title')
    {{ __('Add Money') }}
@endsection

@section('content')
    <div class="col-xxl-12">

        <!-- Page title wrapper -->
        <div class="page-title-wrapper mb-16">
            <div class="page-title-contents">
                <h3 class="page-title">{{ __('Add Money') }}</h3>
            </div>
        </div>
        <!-- Page title wrapper -->

        <!-- Pages navigation start -->
        @include('frontend::user.add_money._topbar')
        <!-- Pages navigation end -->

    </div>
    <div class="col-xxl-6">
        <!-- Add money area start -->
        <div class="add-money-area default-area-style step-one">
            <div class="heading-top">
                <h5 class="title">{{ __('Add Money') }}</h5>
            </div>
            <div class="default-content-inner">
                <form action="{{ route('user.addMoney.now') }}" method="POST" enctype="multipart/form-data"
                    id="addMoneyForm">
                    @csrf

                    <div class="row gy-24">

                        <!-- Wallet Selection -->
                        <div class="col-lg-12">
                            <div class="td-form-group">
                                <label class="input-label" for="user_wallet">{{ __('Wallet') }} <span>*</span></label>
                                <div class="input-field">
                                    <select name="user_wallet" id="user_wallet" class="defaultselect2 "
                                        onchange="userWalletInfo(this)">
                                        <option value="" disabled selected>{{ __('Select Wallet') }}</option>
                                        <option value="default" data-user-wallet-name="{{ __('Main Wallet') }}">
                                            {{ __('Main Wallet') }}
                                        </option>
                                        @foreach ($wallets as $wallet)
                                            <option value="{{ $wallet->id }}"
                                                data-user-wallet-name="{{ $wallet?->coin?->name }}">
                                                {{ $wallet?->coin?->name }} ({{ $wallet?->coin?->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <p class="feedback-invalid wallet-required"></p>
                                @error('user_wallet')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- Payment Gateway -->
                        <div class="col-lg-12">
                            <div class="td-form-group">
                                <label class="input-label">{{ __('Gateway') }} <span>*</span></label>
                                <div class="input-field">
                                    <select name="payment_gateway" id="payment_gateway" class="defaultselect2 ">
                                        <option value="" disabled selected>{{ __('Select Gateway') }}</option>
                                    </select>
                                </div>
                                <p class="feedback-invalid gateway-required"></p>
                                <p class="input-attention danger-text gateway-charge mt-1"></p>
                                @error('payment_gateway')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- Amount -->
                        <div class="col-lg-12">
                            <div class="td-form-group input-fill">
                                <label class="input-label" for="amount">{{ __('Amount') }} <span>*</span></label>
                                <div class="input-field">
                                    <input type="text" name="amount" id="amount" class="form-control box-input" required>
                                </div>
                                <p class="feedback-invalid amount-required"></p>
                                @error('amount')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <!-- Review Data Placeholder -->
                        <div class="col-lg-12">
                            <div class="show-review-data"></div>
                        </div>

                        <!-- Next Step Button -->
                        <div class="col-lg-12">
                            <div class="td-form-btns d-flex mt-30">
                                <button type="button" class="td-btn btn-chip grd-fill-btn-primary next-step-btn">
                                    <span class="btn-text">{{ __('Next Step') }}</span>
                                </button>
                            </div>
                        </div>
                    </div>


                </form>
            </div>
        </div>
        <!-- Review Details -->
        <div class="review-table step-two" style="display: none;">
            <div class="history-details-area">
                <div class="history-table-wrapper table-responsive">
                    <div class="header">{{ __('Review Details') }}</div>
                    <table>
                        <tbody>
                            <tr>
                                <th><strong>{{ __('Amount') }}</strong></th>
                                <td class="amount-text"></td>
                            </tr>
                            <tr>
                                <th><strong>{{ __('Wallet Name') }}</strong></th>
                                <td class="user-wallet-text"></td>
                            </tr>
                            <tr>
                                <th><strong>{{ __('Payment Method') }}</strong></th>
                                <td class="paymentMethod"></td>
                            </tr>
                            <tr>
                                <th><strong>{{ __('Charge') }}</strong></th>
                                <td class="charge-text"></td>
                            </tr>
                            <tr>
                                <th><strong>{{ __('Total') }}</strong></th>
                                <td class="total-text gdt-text-primary"></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="button-wrapper">
                        <button href="" class="td-btn grd-outline-fill-btn primary-btn btn-m-w back-btn">
                            <span class="inner-btn">
                                <span class="btn-text">
                                    {{ __('Back') }}
                                </span>
                            </span>
                        </button>
                        <button form="addMoneyForm" type="submit" class="td-btn btn-chip grd-fill-btn-primary">
                            {{ __('Add Money') }}
                        </button>
                    </div>
                </div>
            </div>

        </div>
        <!-- Add money area end -->
    </div>
@endsection

@push('js')
    <script>
        "use strict";
        const CONFIG = {
            multipleCurrency: "{{ setting('multiple_currency', 'permission') }}",
            isUserWalletTopUp: "{{ request('user_wallet') }}",
            routes: {
                gatewayMethod: "{{ route('user.userWallet.gatewayMethod') }}"
            },
            csrf: "{{ csrf_token() }}"
        };

        const elements = {
            stepOne: $('.step-one'),
            stepTwo: $('.step-two'),
            userWalletSelect: $('select[name="user_wallet"]'),
            paymentGatewaySelect: $('select[name="payment_gateway"]'),
            amountInput: $('input[name="amount"]')
        };

        function validateForm() {
            const userWallet = elements.userWalletSelect.val();
            const gateway = elements.paymentGatewaySelect.val();
            const amount = elements.amountInput.val();
            const minAmount = parseFloat(elements.paymentGatewaySelect.find(':selected').data('minimum-amount').replaceAll(',',''));
            const maxAmount = parseFloat(elements.paymentGatewaySelect.find(':selected').data('maximum-amount').replaceAll(',',''));
            const gatewayCurrency = elements.paymentGatewaySelect.find(':selected').data('gateway-currency');


            if (CONFIG.multipleCurrency && !userWallet) {
                return showError('.wallet-required', '{{ __('User wallet is required') }}');
            }
            if (!gateway) {
                return showError('.gateway-required', '{{ __('Payment gateway is required') }}');
            }
            if (!amount || isNaN(amount) || amount <= 0) {
                return showError('.amount-required', '{{ __('Amount field is required') }}');
            }
            if(amount < minAmount || amount > maxAmount) {
                return showError('.amount-required', `{{ __('Amount must be between') }} ${formatAmount(minAmount, 2)} ${gatewayCurrency} {{ __('and') }} ${formatAmount(maxAmount, 2)} ${gatewayCurrency}`);
            }

            return true;
        }

        function showError(element, message) {
            $(element).show().html(message);
        }

        function clearError(element) {
            $(element).hide().html('');
        }

        function handleNextStep(e) {
            e.preventDefault();
            if (!$(e.currentTarget).closest('.step-one').length) return;

            if (validateForm()) {
                elements.stepOne.hide();
                elements.stepTwo.show();
                $('.active-step-two, .active-line-one').addClass('active');
                $('.show-check-mark-one').html(
                    '<svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.23283 12.8517L3.36475 9.00375L4.33177 8.04177L7.23283 10.9277L13.4591 4.73389L14.4261 5.69587L7.23283 12.8517Z" fill="white"/></svg>'
                );
            }
        }

        async function getGateways() {
            try {
                clearError('.gateway-required');
                const response = await $.ajax({
                    type: "POST",
                    url: CONFIG.routes.gatewayMethod,
                    data: {
                        wallet_currency: elements.userWalletSelect.val(),
                        gateway_type: 'add_money',
                        _token: CONFIG.csrf
                    }
                });

                if (response.options) {
                    elements.paymentGatewaySelect.html(response.options)

                    elements.amountInput.val('');
                    $('.amount-text, .charge-text, .total-text, .payTotal-text').html('');
                    $('.amount-required').html('');
                    $('.show-review-data').html('');
                }
            } catch (error) {
                console.log(error);
                showError('.gateway-required', '{{ __('Failed to load payment gateways') }}');
            }
        }

        function handleAmountChange() {
            const amount = parseFloat(elements.amountInput.val());

            const selectedOption = elements.paymentGatewaySelect.find(':selected');
            if (!selectedOption.length) {
                return showError('.amount-required', '{{ __('Please select your payment gateway first') }}');
            }

            const gatewayData = {
                minAmount: parseFloat(selectedOption.attr('data-minimum-amount')) || 0,
                maxAmount: parseFloat(selectedOption.attr('data-maximum-amount')) || 0,
                charge: parseFloat(selectedOption.attr('data-gateway-charge')) || 0,
                chargeType: selectedOption.attr('data-gateway-charge-type'),
                amountRate: parseFloat(selectedOption.attr('data-gateway-rate')) || 1,
                currency: selectedOption.attr('data-gateway-currency'),
                decimals: parseInt(selectedOption.attr('data-gateway-decimals')) || 2,
                paymentMethod: selectedOption.attr('data-payment-method')
            };


            if (amount < gatewayData.minAmount || amount > gatewayData.maxAmount) {
                showError('.amount-required',
                    `Amount must be between ${formatAmount(gatewayData.minAmount, gatewayData.decimals)} and ${formatAmount(gatewayData.maxAmount, gatewayData.decimals)} ${gatewayData.currency}`
                );
            }

            clearError('.amount-required');


            const charge = gatewayData.chargeType === 'percentage' ?
                (amount * gatewayData.charge / 100) :
                gatewayData.charge;

            const total = Number(amount) + Number(charge);
            const payTotal = (total * gatewayData.amountRate);


            const formattedAmount = formatAmount(amount, gatewayData.decimals);
            const formattedCharge = formatAmount(charge, gatewayData.decimals);
            const formattedTotal = formatAmount(total, gatewayData.decimals);
            const formattedPayTotal = formatAmount(payTotal, gatewayData.decimals);


            $('.amount-text').html(`${formattedAmount} ${gatewayData.currency}`);
            $('.charge-text').html(`${formattedCharge} ${gatewayData.currency}`);
            $('.total-text').html(`${formattedTotal} ${gatewayData.currency}`);
            $('.payTotal-text').html(`${formattedPayTotal} ${gatewayData.currency}`);
            $('.paymentMethod').text(gatewayData.paymentMethod || '');


            $('#add-amount').val(formattedAmount);


            $('.chargeShow').html(`${formattedCharge} ${gatewayData.currency}`);
        }

        function updateGatewayInfo() {

            $('.amount-required').text('');
            $('.show-review-data').html('');
            $('.gateway-required').text('');

            const selectedOption = elements.paymentGatewaySelect.find(':selected');


            if (!selectedOption.length || selectedOption.is(':disabled')) {
                showError('.gateway-required', '{{ __('Please select a valid payment gateway') }}');
                return;
            }


            const data = {
                minAmount: selectedOption.data('minimum-amount'),
                maxAmount: selectedOption.data('maximum-amount'),
                routeUrl: selectedOption.data('routeurl'),
                gatewayRate: selectedOption.data('gateway-rate'),
                paymentMethod: selectedOption.data('payment-method'),
                gatewayCurrency: selectedOption.data('gateway-currency')
            };
            var chargeFormatted = selectedOption.data('gateway-charge-type') == 'percentage' ? formatAmount(selectedOption.data('gateway-charge'), {{ $currencyDecimals }}) : selectedOption.data('gateway-charge');


            showError('.amount-required', `{{ __('Minimum') }} ${data.minAmount} ${data.gatewayCurrency} {{ __('and Maximum') }} ${data.maxAmount} ${data.gatewayCurrency}`);
            $(".conversionRate").text(data.gatewayRate);
            $(".gateway-charge").text("{{ __('Charge') }}: " + chargeFormatted +
     (selectedOption
     .data('gateway-charge-type') == 'percentage' ? '%' : ' {{ $currency }}'));

            $.get(data.routeUrl)
                .done(response => {
                    $(".show-review-data").html(response);

                    if ($('input[name="amount"]').val()) {
                        handleAmountChange();
                    }
                })
                .fail(() => {
                    showError('.gateway-required', '{{ __('Failed to load gateway details') }}');
                });
        }

        $(elements.paymentGatewaySelect).on('change', function () {
            updateGatewayInfo();
        });
        @if (request('user_wallet'))
            elements.userWalletSelect.val("{{ request('user_wallet') }}");
            getGateways();
        @endif


        $(document).ready(() => {

            elements.stepOne.show();
            elements.stepTwo.hide();


            $('.error-message').html('');


            $('.next-step-btn').on('click', handleNextStep);

            $('.back-btn').on('click', () => {
                elements.stepOne.show();
                elements.stepTwo.hide();
                $('.active-step-two').removeClass('active');
                $('.show-check-mark-one').empty();
            });
            elements.userWalletSelect.on('change', getGateways);
            elements.amountInput.on('input', handleAmountChange);


            window.userWalletInfo = element => {
                if (element && element.value) {
                    const walletName = $(element).find(':selected').data('user-wallet-name');
                    $('.user-wallet-text').html(walletName || '');
                }
            };
        });

        function formatAmount(amount, decimals) {
            const factor = Math.pow(10, decimals);
            const rounded =
                Math.round((Number(amount) + Number.EPSILON) * factor) / factor;

            return rounded.toFixed(decimals);
        }
    </script>
@endpush