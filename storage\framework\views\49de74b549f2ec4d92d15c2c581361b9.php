<h3 class="title mb-4">
    <?php echo e(__('Deposit Approval Action')); ?>

</h3>
<?php use \App\Enums\TxnType; ?>

<ul class="list-group mb-4">
    <li class="list-group-item">
        <?php echo e($data?->user?->role . ' Name :'); ?>

        <strong>
            <?php echo e($data?->user?->first_name . ' ' . $data?->user?->last_name); ?>

        </strong>
    </li>
    <li class="list-group-item">
        <?php echo e(__('Total amount :')); ?> <strong>
            <?php if(in_array($data->type, [TxnType::PlanPurchase->value, TxnType::PlanPurchaseManual->value])): ?>
            <?php echo e(formatAmount($data->final_amount).' '.$currency); ?></strong>
                <?php else: ?>
                <?php echo e(trxAmountFormat($data, 'final_amount')); ?></strong>
            <?php endif; ?>
    </li>
    <li class="list-group-item">
        <?php echo e(__('Charge :')); ?> <strong><?php echo e(trxAmountFormat($data, 'charge')); ?></strong>
    </li>
    <li class="list-group-item">
        <?php echo e(__('Paid Amount :')); ?> <strong><?php echo e(trxAmountFormat($data, 'pay_amount')); ?></strong>
    </li>
    <li class="list-group-item">
        <?php echo e(__('Wallet :')); ?>

        <strong>
            <?php if($data?->wallet_type == null || $data?->wallet_type == 'default'): ?>
                <?php echo e(__('Main Wallet')); ?>

            <?php else: ?>
                <?php echo e($data?->wallet?->coin?->name); ?>

            <?php endif; ?>
        </strong>
    </li>
    <li class="list-group-item">
        <?php echo e(__('Transaction Type :')); ?>

        <strong class="site-badge primary">
            <?php echo e($data->type_text); ?>

        </strong>
    </li>
</ul>

<ul class="list-group mb-4">
    <?php $__currentLoopData = $data->manual_field_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="list-group-item">
            <?php echo e($key); ?>:
            <?php if($value != new stdClass()): ?>
                <?php if(file_exists(public_path('' . $value))): ?>
                    <a target="__blank" href="<?php echo e(asset($value)); ?>">
                        <img src="<?php echo e(asset($value)); ?>" alt="<?php echo e($key); ?>" />
                    </a>
                <?php else: ?>
                    <strong><?php echo e($value); ?></strong>
                <?php endif; ?>
            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<form action="<?php echo e(route('admin.deposit.action.now')); ?>" method="post">
    <?php echo csrf_field(); ?>
    <input type="hidden" name="id" value="<?php echo e($id); ?>">

    <div class="site-input-groups">
        <label for="" class="box-input-label"><?php echo e(__('Details Message(Optional)')); ?></label>
        <textarea name="message" class="form-textarea mb-0" placeholder="Details Message"></textarea>
    </div>

    <div class="action-btns">
        <button type="submit" name="approve" value="yes" class="site-btn-sm primary-btn me-2">
            <i data-lucide="check"></i>
            <?php echo e(__('Approve')); ?>

        </button>
        <button type="submit" name="reject" value="yes" class="site-btn-sm red-btn">
            <i data-lucide="x"></i>
            <?php echo e(__('Reject')); ?>

        </button>
    </div>
</form>
<script>
    'use strict';
    lucide.createIcons();
</script><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/deposit/include/__deposit_action.blade.php ENDPATH**/ ?>