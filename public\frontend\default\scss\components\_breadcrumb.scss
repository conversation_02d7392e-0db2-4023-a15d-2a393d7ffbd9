@use "../utils" as *;

/*----------------------------------------*/
/* Breadcrumb styles
/*----------------------------------------*/
.breadcrumb-wrapper {
  backdrop-filter: blur(5px);
  padding: 30px 30px;
  background-color: #F2F2F2;

  @media #{$xs} {
    padding: 25px 20px;
  }

  @include dark-theme {
    background-color: #0E1B42;
  }
}

.breadcrumb-heading {
  .title {
    font-size: 30px;

    @media #{$sm,$md,$lg} {
      font-size: 28px;
    }

    @media #{$xs} {
      font-size: 24px;
    }
  }
}

.breadcrumb-menu {
  ul {
    @include inline-flex();
    gap: 20px;
    justify-content: center;

    li {
      list-style: none;
      position: relative;
      line-height: 1;

      &:last-child {
        span {
          background: var(--Gradiant3, linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%));
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      &:not(:last-child) {
        &::before {
          display: inline-block;
          content: "";
          position: absolute;
          top: 50%;
          height: 18px;
          width: 1px;
          inset-inline-end: -11px;
          font-size: 18px;
          background-color: rgba($black, $alpha: 0.3);
          transform: translateY(-50%) rotate(15deg);

          @include dark-theme {
            background-color: rgba($white, $alpha: 0.3);
          }
        }
      }

      &.active {
        span {
          color: var(--bd-theme-primary);
        }
      }

      span {
        font-size: 14px;
        font-family: var(--td-ff-heading);
        text-transform: capitalize;
        font-weight: 600;

        a {
          font-weight: var(--bd-fw-medium);

          &:hover {
            color: var(--bd-theme-primary);
          }
        }
      }
    }
  }
}