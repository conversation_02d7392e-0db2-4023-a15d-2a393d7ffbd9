<?php $__env->startSection('title'); ?>
    <?php echo e($title); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <div class="title-content">
                            <h2 class="title"><?php echo e($title); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-xl-12">
                    <div class="site-table table-responsive">
                        <form action="<?php echo e(request()->url()); ?>" method="get">
                            <div class="table-filter">
                                <div class="filter">
                                    <div class="search">
                                        <input type="text" id="search" name="query" value="<?php echo e(request('query')); ?>"
                                            placeholder="Search" />
                                    </div>
                                    <select name="email_status" id="email_status" class="form-select form-select-sm">
                                        <option value="" selected><?php echo e(__('Filter By Email Status')); ?></option>
                                        <option value="verified"
                                            <?php echo e(request('email_status') == 'verified' ? 'selected' : ''); ?>>
                                            <?php echo e(__('Email Verified')); ?></option>
                                        <option value="unverified"
                                            <?php echo e(request('email_status') == 'unverified' ? 'selected' : ''); ?>>
                                            <?php echo e(__('Email Unverified')); ?></option>
                                    </select>
                                    <select name="kyc_status" id="kyc_status" class="form-select form-select-sm">
                                        <option value="" selected><?php echo e(__('Filter By KYC')); ?></option>
                                        <option value="1" <?php echo e(request('kyc_status') == '1' ? 'selected' : ''); ?>>
                                            <?php echo e(__('Verified')); ?></option>
                                        <option value="0" <?php echo e(request('kyc_status') == '0' ? 'selected' : ''); ?>>
                                            <?php echo e(__('Unverified')); ?></option>
                                    </select>

                                    <select name="status" id="status" class="form-select form-select-sm">
                                        <option value="" selected><?php echo e(__('Filter By Status')); ?></option>
                                        <option value="1" <?php echo e(request('status') == '1' ? 'selected' : ''); ?>>
                                            <?php echo e(__('Active')); ?></option>
                                        <option value="0" <?php echo e(request('status') == '0' ? 'selected' : ''); ?>>
                                            <?php echo e(__('Disabled')); ?></option>
                                        <option value="2" <?php echo e(request('status') == '2' ? 'selected' : ''); ?>>
                                            <?php echo e(__('Closed')); ?></option>
                                    </select>
                                    <button type="submit" class="apply-btn"><i
                                            data-lucide="search"></i><?php echo e(__('Search')); ?></button>
                                </div>
                            </div>
                        </form>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Avatar')); ?></th>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'User',
                                        'field' => 'username',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', ['label' => 'Email', 'field' => 'email'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'Main Balance',
                                        'field' => 'balance',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <th><?php echo e(__('Email Status')); ?></th>
                                    <th><?php echo e(__('KYC')); ?></th>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'Status',
                                        'field' => 'status',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <?php echo $__env->make('backend.user.include.__avatar', [
                                                'avatar' => $user->avatar,
                                                'first_name' => $user->first_name,
                                                'last_name' => $user->last_name,
                                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('admin.user.edit', $user->id)); ?>"
                                                class="link"><?php echo e(Str::limit($user->username, 15)); ?></a>
                                        </td>
                                        <td><?php echo e(Str::limit($user->email, 20)); ?></td>
                                        <td>
                                            <?php echo e(formatAmount($user->balance, $currency, true)); ?>

                                        </td>
                                        <td>
                                            <?php if($user->email_verified_at != null): ?>
                                                <div class="site-badge success"><?php echo e(__('Verified')); ?></div>
                                            <?php else: ?>
                                                <div class="site-badge pending"><?php echo e(__('Unverified')); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo $__env->make('backend.user.include.__kyc', ['kyc' => $user->kyc], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        </td>
                                        <td>
                                            <?php echo $__env->make('backend.user.include.__status', [
                                                'status' => $user->status,
                                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        </td>
                                        <td>
                                            <?php echo $__env->make('backend.user.include.__action', ['user' => $user], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <td colspan="8" class="text-center"><?php echo e(__('No Data Found!')); ?></td>
                                <?php endif; ?>
                            </tbody>
                            <!-- Modal for Send Mail -->
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('customer-mail-send')): ?>
                                <?php echo $__env->make('backend.user.include.__mail_send', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php endif; ?>
                            <!-- Modal for Send Mail End-->
                        </table>

                        <?php echo e($users->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal for Popup Box -->
    <?php echo $__env->make('backend.user.include.__delete_popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Popup Box End-->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        (function($) {
            "use strict";

            //send mail modal form open
            $('body').on('click', '.send-mail', function() {
                var id = $(this).data('id');
                var name = $(this).data('name');
                $('#name').html(name);
                $('#userId').val(id);
                $('#sendEmail').modal('toggle')
            })

            // Delete
            $('body').on('click', '#deleteModal', function() {
                var id = $(this).data('id');
                var name = $(this).data('name');

                $('#data-name').html(name);
                var url = '<?php echo e(route('admin.user.destroy', ':id')); ?>';
                url = url.replace(':id', id);
                $('#deleteForm').attr('action', url);
                $('#delete').modal('toggle')
            });

        })(jQuery);
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/user/index.blade.php ENDPATH**/ ?>