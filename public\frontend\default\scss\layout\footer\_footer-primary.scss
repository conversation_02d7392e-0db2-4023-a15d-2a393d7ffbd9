@use '../../utils' as *;

/*----------------------------------------*/
/* Footer primary style
/*----------------------------------------*/
.footer-primary {
  position: relative;
  z-index: 5;
  background: #E6EFFC;

  @include dark-theme {
    background-color: var(--td-void);
  }

  .footer-bg {
    position: absolute;
    bottom: 0;
    inset-inline-start: 0;
    z-index: -1;
    width: 448px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: left;
    background-size: 100% 100%;
    opacity: 0.3;
  }

  .footer-main {
    padding: 80px 0 60px;
  }

  .footer-logo {
    .logo-white {
      display: none;
    }
  }
}

// Footer widget
.footer-widget {
  &.footer-col-one {
    max-width: 260px;
  }

  &.footer-col-two {
    padding-inline-start: 90px;

    @media #{$xl} {
      padding-inline-start: 35px;
    }

    @media #{$xs,$sm,$md,$lg} {
      padding-inline-start: 0px;
    }
  }

  &.footer-col-three {
    padding-inline-start: 50px;

    @media #{$xs,$sm,$md,$lg} {
      padding-inline-start: 0px;
    }
  }

  &.footer-col-four {
    padding-inline-start: 20px;

    @media #{$xs,$sm,$md,$lg} {
      padding-inline-start: 0px;
    }
  }
}

// Footer widget head
.footer-wg-head {
  .title {
    color: var(--td-heading);
    margin-bottom: 14px;
    font-size: 20px;
    position: relative;
    padding-bottom: 14px;
    font-weight: 700;

    &::before {
      position: absolute;
      content: "";
      height: 1px;
      width: 54px;
      bottom: 0;
      inset-inline-start: 0;
      background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
    }
  }
}

// Footer links
.footer-links {
  ul {
    li {
      list-style: none;

      &:not(:last-child) {
        margin-bottom: 12px;
      }

      a {
        font-size: 16px;
        font-weight: 600;
        background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: #47494E;

        @include dark-theme {
          color: #9A9DA7;
        }

        &:hover {
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}

// Footer social
.footer-social {
  @include flexbox();
  gap: 16px;
  flex-direction: column;

  ul {
    @include flexbox();
    gap: 16px;

    li {
      list-style: none;

      a {
        font-size: 20px;
      }
    }
  }
}

// Footer info
.footer-info {
  .info-item {
    display: flex;
    align-items: self-start;
    gap: 10px;

    &:not(:last-child) {
      margin-bottom: 18px;
    }

    .icon {
      width: 20px;
      height: 20px;
      flex: 0 0 auto;
    }

    .text {
      a {
        background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
        -webkit-background-clip: text;
        background-clip: text;

        &:hover {
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}