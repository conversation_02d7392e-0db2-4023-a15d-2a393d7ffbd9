<?php $__env->startSection('title'); ?>
    <?php echo e(__('Support Tickets')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="col-xxl-12">
    <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
           <h3 class="page-title"><?php echo e(__('Support Tickets')); ?></h3>
        </div>
     </div>
     <?php echo $__env->make('frontend::user.ticket.include.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<div class="col-xxl-12">
    <div class="payment-history-area">
       <div class="recent-history-table">
          <div class="table-container table-responsive"> 
            <table class="td-table recent-table">
                <thead>
                    <tr>
                        <th><?php echo e(__('Ticket Title')); ?></th>
                        <th><?php echo e(__('Status')); ?></th>
                        <th><?php echo e(__('Action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $tickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="table-description">
                                    <div class="icon">
                                        <span><iconify-icon icon="tabler:cast"></iconify-icon></span>
                                    </div>
                                    <div class="contents">
                                        <h6 class="title"><?php echo e($ticket->title); ?></h6>
                                        <span class="date"><?php echo e(\Carbon\Carbon::parse($ticket->created_at)->format('d M Y h:i A')); ?></span>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php if($ticket->status == 'open'): ?>
                                    <span class="td-badge badge-success"><?php echo e(ucfirst($ticket->status)); ?></span>
                                <?php elseif($ticket->status == 'pending'): ?>
                                    <span class="td-badge badge-warning"><?php echo e(ucfirst($ticket->status)); ?></span>
                                <?php else: ?>
                                    <span class="td-badge badge-danger"><?php echo e(ucfirst($ticket->status)); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="table-actions">
                                    <a href="<?php echo e(route('user.ticket.show', $ticket->uuid)); ?>"
                                       class="td-btn btn-xs grd-fill-btn-primary radius-30"
                                       data-bs-toggle="tooltip" data-bs-title="<?php echo e(__('Details')); ?>">
                                        <span class="btn-icon"><iconify-icon icon="tabler:send"></iconify-icon></span>
                                        <span class="btn-text"><?php echo e(__('View')); ?></span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="3" class="text-center">
                                <?php if (isset($component)) { $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.no-data-found','data' => ['class' => 'mt-10','module' => ''.e(__('Ticket')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('no-data-found'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mt-10','module' => ''.e(__('Ticket')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $attributes = $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $component = $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>  
        <?php echo e($tickets->links()); ?>

            
       </div>   
    </div>
 </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/ticket/index.blade.php ENDPATH**/ ?>