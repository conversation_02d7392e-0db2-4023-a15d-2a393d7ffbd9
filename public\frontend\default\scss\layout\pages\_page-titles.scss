@use '../../utils' as *;

/*----------------------------------------*/
/* Page titles styles
/*----------------------------------------*/

.page-title-wrapper {
    padding-bottom: 16px;
    border-bottom: 2px solid rgba($heading, $alpha: 0.1);

    @include dark-theme {
        border-color: rgba($white, $alpha: 0.1);
    }

    .page-title {
        font-size: 24px;

        @media #{$xs} {
            font-size: rem(20);
        }
    }
}

.pages-navigation-nav {
    position: relative;
    display: inline-block;
    padding: 1px;

    .pages-navigation-items {
        position: relative;
        display: inline-flex;
        flex-wrap: wrap;
        z-index: 3;
        background: var(--td-white);
        clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
        gap: 12px;
        padding: 10px;

        @include dark-theme {
            background: var(--td-void);
        }

        &::before {
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            content: "";
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.05) -19.04%, rgba(9, 70, 255, 0.03) 53.33%);
            z-index: -1;
            width: 100%;
            height: 100%;

            @include dark-theme {
                background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -19.04%, rgba(9, 70, 255, 0.06) 53.33%);

            }
        }

        .td-btn {
            height: 36px;
        }
    }

    &::before,
    &::after {
        position: absolute;
        inset-inline-start: 0;
        top: 0;
        transition: all .3s;
        width: 100%;
        height: 100%;
        background: linear-gradient(0deg, #D9DADC -19.04%, #D9DADC 53.33%);
        content: "";
        clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
        border-radius: 2px;

        @include dark-theme {
            background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
        }
    }

    &::after {
        background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
        opacity: 0;
        visibility: hidden;
    }

    &:hover::after {
        opacity: 1;
        visibility: visible;
    }
}