<!-- Recent transaction section start -->
@use(App\Models\Transaction)
@use(App\Enums\TxnType)
@use(App\Enums\TxnStatus)

@php
    $payments = Transaction::whereIn('type',[TxnType::PlanPurchase])->latest()->take(5)->get();
    $withdraw = Transaction::whereIn('type',[TxnType::Withdraw, TxnType::WithdrawAuto])->with(['wallet.coin'])->latest()->take(5)->get();
@endphp
<section class="td-recent-transaction-area p-relative zi-11 section_space include-bg section_space-bottom">
    <div class="container">
       <div class="row justify-content-center">
          <div class="col-xxl-6 col-xl-6 col-lg-6">
             <div class="section-title-wrapper text-center section_title_space">
                <h2 class="section-title has_text_move_anim mb-15">{!! highlightColor($data['hero_title']) !!}</h2>
                <p class="description has_fade_anim">{{ $data['sub_title'] }}</p>
             </div>
          </div>
       </div>
       <div class="row gy-30 justify-content-center">
          <div class="col-xxl-6 col-xl-6 col-lg-6">
             <div class="recent-transactions-box has_fade_anim" data-fade-from="left">
                <div class="clip-path">
                   <div class="clip-path-inner">
                      <div class="transactions-heading mb-14">
                         <h4 class="title">{{ __('Latest Payment') }}</h4>
                      </div>
                      <div class="transaction-contents table-responsive">
                         <div class="td-custom-table">
                            <div class="contents">
                               <div class="site-table-list site-table-head" data-background="{{ frontendAsset('images/bg/table-head.png') }}">
                                  <div class="site-table-col">{{ __('User') }}</div>
                                  <div class="site-table-col">{{ __('Amount') }}</div>
                               </div>
                               @foreach ($payments as $payment)
                               <div class="site-table-list">
                                  <div class="site-table-col">
                                     <span class="fw-7">{{ $payment->user->full_name }}</span>
                                  </div>
                                  <div class="site-table-col">
                                    <div class="right-two-grid">
                                        <span class="td-badge badge-outline-{{ $payment->status->value == TxnStatus::Success->value ? 'success' : 'warning' }}">{{ $payment->status }}</span>
                                        <span class="fw-7">{{ formatAmount($payment->amount, $payment->wallet?->coin, true) }}</span>
                                    </div>
                                   </div>
                               </div>
                               @endforeach

                            </div>
                         </div>
                      </div>
                   </div>
                </div>
                <div class="bg-shape" data-background="{{ frontendAsset('images/bg/recent-dash-bg.png') }}">
                </div>
             </div>
          </div>
          <div class="col-xxl-6 col-xl-6 col-lg-6">
             <div class="recent-transactions-box has_fade_anim" data-fade-from="right">
                <div class="clip-path">
                   <div class="clip-path-inner">
                      <div class="transactions-heading mb-14">
                         <h4 class="title">{{ __('Latest Withdraw') }}</h4>
                      </div>
                      <div class="transaction-contents table-responsive">
                         <div class="td-custom-table">
                            <div class="contents">
                                <div class="site-table-list site-table-head" data-background="{{ frontendAsset('images/bg/table-head.png') }}">
                                   <div class="site-table-col">{{ __('User') }}</div>
                                   <div class="site-table-col">{{ __('Amount') }}</div>
                                </div>
                                @foreach ($withdraw as $payment)
                                <div class="site-table-list">
                                   <div class="site-table-col">
                                      <span class="fw-7">{{ $payment->user->full_name }}</span>
                                   </div>
                                   <div class="site-table-col">
                                    <div class="right-two-grid">
                                        <span class="td-badge badge-outline-{{ $payment->status->value == TxnStatus::Success->value ? 'success' : 'warning' }}">{{ $payment->status }}</span>
                                        <span class="fw-7">{{ formatAmount($payment->amount, $payment->wallet?->coin, true) }}</span>
                                    </div>
                                   </div>
                                </div>
                                @endforeach
 
                             </div>
                         </div>
                      </div>
                   </div>
                </div>
                <div class="bg-shape" data-background="{{ frontendAsset('images/bg/recent-dash-bg.png') }}">
                </div>
             </div>
          </div>
       </div>
    </div>
    <div class="recent-transition-bg" data-background="{{ frontendAsset('images/bg/recent-transaction-bg.png') }}"></div>

 </section>
 <!-- Recent transaction section end -->