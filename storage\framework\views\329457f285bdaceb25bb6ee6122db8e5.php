<?php $__env->startSection('title'); ?>
    <?php echo e(__('Pending Manual Deposit')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('deposit_content'); ?>
    <div class="col-xl-12 col-md-12">
        <div class="site-card">
            <div class="site-card-body table-responsive">
                <div class="site-table table-responsive">
                    <?php echo $__env->make('backend.deposit.include.__filter', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <?php echo $__env->make('backend.filter.th', ['label' => 'Date', 'field' => 'created_at'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php echo $__env->make('backend.filter.th', ['label' => 'User', 'field' => 'user'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <th><?php echo e(__('Type')); ?></th>
                                <?php echo $__env->make('backend.filter.th', [
                                    'label' => 'Transaction ID',
                                    'field' => 'tnx',
                                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php echo $__env->make('backend.filter.th', ['label' => 'Amount', 'field' => 'amount'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php echo $__env->make('backend.filter.th', ['label' => 'Charge', 'field' => 'charge'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php echo $__env->make('backend.filter.th', ['label' => 'Gateway', 'field' => 'method'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php echo $__env->make('backend.filter.th', ['label' => 'Wallet', 'field' => 'wallet'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <?php echo $__env->make('backend.filter.th', ['label' => 'Status', 'field' => 'status'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                <th><?php echo e(__('Action')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $deposits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $deposit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <?php echo e($deposit->created_at); ?>

                                    </td>
                                    <td>
                                        <?php echo $__env->make('backend.transaction.include.__user', [
                                            'id' => $deposit->user_id,
                                            'name' => $deposit->user->username,
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    </td>
                                    <td>
                                        <span class="site-badge primary"><?php echo e($deposit->type_text); ?></span>
                                    </td>
                                    <td><?php echo e(safe($deposit->tnx)); ?></td>
                                    <td>
                                        <?php echo e(trxAmountFormat($deposit, 'pay_amount')); ?>

                                    </td>
                                    <td>
                                        <?php echo e(trxAmountFormat($deposit, 'charge')); ?>

                                    </td>
                                    <td>
                                        <?php echo e(safe($deposit->method)); ?>

                                    </td>
                                    <td>
                                        <?php if($deposit->wallet_type == null || $deposit->wallet_type == 'default'): ?>
                                            <?php echo e(__('Main Walllet')); ?>

                                        <?php else: ?>
                                            <?php echo e($deposit?->wallet?->coin?->name); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo $__env->make('backend.transaction.include.__txn_status', [
                                            'status' => $deposit->status->value,
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    </td>
                                    <td>
                                        <?php echo $__env->make('backend.deposit.include.__action', [
                                            'id' => $deposit->id,
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <td colspan="8" class="text-center"><?php echo e(__('No Data Found!')); ?></td>
                            <?php endif; ?>
                        </tbody>
                    </table>

                    <?php echo e($deposits->links('backend.include.__pagination')); ?>

                </div>
            </div>
        </div>
        <!-- Modal for Pending Deposit Approval -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('deposit-action')): ?>
            <div class="modal fade" id="deposit-action-modal" tabindex="-1" aria-labelledby="editPendingDepositModalLabel"
                aria-hidden="true">
                <div class="modal-dialog modal-md modal-dialog-centered">
                    <div class="modal-content site-table-modal">
                        <div class="modal-body popup-body">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            <div class="popup-body-text deposit-action">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Modal for Pending Deposit Approval -->
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    <script>
        (function($) {
            "use strict";

            let loader =
                '<div class="text-center"><i data-lucide="loader-circle" class=" spining-icon"> </i> <?php echo e(__('Please wait..')); ?></div>';

            //send mail modal form open
            $('body').on('click', '#deposit-action', function() {
                $('.deposit-action').html(loader);
                var id = $(this).data('id');
                var url = '<?php echo e(route('admin.deposit.action', ':id')); ?>';
                url = url.replace(':id', id);
                $.get(url, function(data) {
                    $('.deposit-action').html(data)
                    imagePreview()
                });

                $('#deposit-action-modal').modal('toggle');
            })


        })(jQuery);
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.deposit.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/deposit/manual.blade.php ENDPATH**/ ?>