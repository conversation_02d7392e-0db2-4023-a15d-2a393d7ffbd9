@use('App\Models\Coin')
@use('App\Models\Scheme')
@php
   $scheme = Scheme::with('miner.coin')->where('status', true)->get();
   $coins = Coin::active()->get();
@endphp
<div class="pricing-tab-wrapper has_fade_anim  mb-30">
    <div class="pricing-tab-filter td-tab {{ Route::is('user.mining.*') ? '' : 'style-two' }}">
      <div class="inner">
        <nav>
           <div class="nav nav-tabs" id="nav-tab" role="tablist">
             @foreach ($coins as $coin)
            <button class="nav-link {{ $loop->index == 0 ? ' active' : '' }}" id="nav-mining-{{str($coin->code)->slug('-')}}-tab" data-bs-toggle="tab"
              data-bs-target="#nav-mining-{{str($coin->code)->slug('-')}}" type="button" role="tab" aria-controls="nav-mining-{{str($coin->code)->slug('-')}}"
              
              aria-selected="true">
              <div class="inner">
               <span class="btn-icon"><img src="{{ asset($coin->icon) }}" alt=""></span>
               <span class="btn-text">{{ $coin->name }}</span>
              </div>
            </button>
          @endforeach
           </div>
        </nav>
      </div>
    </div>
  </div>
  <div class="pricing-tab-contents">
    <div class="tab-content" id="nav-tabContent">
      @foreach ($scheme->groupBy(['miner.coin_id', 'miner.coin.code']) as $coinId => $schemes)
           @php
           $schemes = $schemes->first();
           $coinSymbol = $schemes->first()->miner->coin->code;              
        @endphp
       <div class="tab-pane fade {{ $loop->index == 0 ? 'show active' : '' }}" id="nav-mining-{{str($schemes->first()->miner->coin->code)->slug('-')}}" role="tabpanel"
        aria-labelledby="nav-mining-{{str($schemes->first()->miner->coin->code)->slug('-')}}-tab" tabindex="0">
        <div class="row gy-50">
           @foreach ($schemes as $key => $scheme)
           <div class="col-lg-{{ Route::is('home') ? 4 : 3 }} col-md-6">
                <div class="pricing-item {{ Route::is('user.mining.*') ? '' : 'style-two' }} has_fade_anim" data-delay="{{ 0.15 * $key+1 }}">
                    <div class="pricing-card">
                        <div class="pricing-clip-path">
                            <div class="pricing-card-inner">
                                <div class="plan-contents">
                                    <h2 class="plan-title">{{ $scheme->name }}</h2>
                                    <p class="plan-subtitle">{{ $scheme->miner->name }}</p>
                                    <div class="plan-price">
                                        {{ formatAmount($scheme->price, $currencySymbol, true) }}
                                    </div>
                                    <div class="features-list">
                                        <ul>
                                         <ul class="feature-list">
                                            <li>
                                                <span class="feature-check">
                                                    <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                </span>
                                                <span class="feature-text">
                                                    {{ __('Return Amount') }}:
                                                    {{ $scheme->return_amount_type == 'fixed' ?
                                                       formatAmount($scheme->return_amount_value, $scheme->miner->coin->code, true) :
                                                       formatAmount($scheme->return_min_amount, $scheme->miner->coin->code, true) . ' - ' .
                                                       formatAmount($scheme->return_max_amount, $scheme->miner->coin->code, true) }}
                                                       / {{ __('per') }} {{ $scheme->return_period_hours }} {{ __('Hours') }}
                                                </span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                </span>
                                                <span class="feature-text">{{ __('Uptime') }}: {{ $scheme->miner->uptime }}%</span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                </span>
                                                <span class="feature-text">{{ __('Renewable Energy') }}: {{ $scheme->miner->renewable_energy }}%</span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                </span>
                                                <span class="feature-text">{{ __('Return Period Type') }}: {{ $scheme->schedule->name }}</span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                </span>
                                                <span class="feature-text">{{ __('Return Period') }}: {{ $scheme->return_period_type == 'period' ? ($scheme->return_period_max_number . ' ' . __('times') ): __('Lifetime') }}</span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                </span>
                                                <span class="feature-text">{{ __('Network Hashrate') }}: {{ $scheme->miner->network_hashrate_amount }} {{ $scheme->miner->network_hashrate }}</span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                </span>
                                                <span class="feature-text">Speed: {{ $scheme->speed_amount }} {{ $scheme->speed }}</span>
                                            </li>                                      
                                            <li>
                                                <span class="feature-check">
                                                    <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                </span>
                                                <span class="feature-text">
                                                    {{ $scheme->maintenance_fee_type == 'percentage' ? $scheme->maintenance_fee_amount . '%' : formatAmount($scheme->maintenance_fee_amount, $scheme->miner->coin->code, true) }}
                                                    {{ __('Maintenance Fee') }}
                                                </span>
                                            </li>
                                        
                                            @if($scheme->max_mining_amount > 0)
                                                <li>
                                                    <span class="feature-check">
                                                        <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                    </span>
                                                    <span class="feature-text">
                                                        {{ formatAmount($scheme->max_mining_amount, $scheme->miner->coin->code, true) }} {{ __('Max Mining Amount') }}
                                                    </span>
                                                </li>
                                            @endif
                                        
                                            @if($scheme->is_featured)
                                                <li>
                                                    <span class="feature-check">
                                                        <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                    </span>
                                                    <span class="feature-text">{{ __('Featured Plan') }}</span>
                                                </li>
                                            @endif
                                        
                                            @if(!empty($scheme->holidays))
                                                <li>
                                                    <span class="feature-check">
                                                        <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                    </span>
                                                    <span class="feature-text">{{ __('Holidays: ') . implode(', ', $scheme->holidays) }}</span>
                                                </li>
                                            @endif              
                                            @if(!empty($scheme->features) && is_array($scheme->features))
                                                @foreach($scheme->features as $feature)
                                                    <li>
                                                        <span class="feature-check">
                                                            <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                        </span>
                                                        <span class="feature-text">{{ $feature }}</span>
                                                    </li>
                                                @endforeach
                                            @else
                                                <li>
                                                    <span class="feature-check">
                                                        <img src="{{ frontendAsset('images/icons/check.svg') }}" alt="check icon">
                                                    </span>
                                                    <span class="feature-text">{{ __('Standard mining operations') }}</span>
                                                </li>
                                            @endif
                                        </ul>
                                    </div>
                                </div>
                                <div class="plan-link">
                                    <a href="{{ route('user.plan-purchase.checkout',$scheme->id) }}" class="td-btn btn-chip grd-fill-btn-secondary w-100">{{ __('Get Started') }}</a>
                                </div>
                            </div>
                        </div>
                        <div class="plan-icon" data-background="{{ frontendAsset('images/icons/gradient-border.png') }}" style="background-image: url('{{ frontendAsset('images/icons/gradient-border.png') }}');">
                            <img src="{{ asset($scheme->icon) }}" alt="{{ $scheme->name }}">
                        </div>
                        <div class="bg-shape" data-background="{{ frontendAsset('images/pricing/price-dot-bg.png') }}" style="background-image: url('{{ frontendAsset('images/pricing/price-dot-bg.png') }}');"></div>
                    </div>
                </div>
          </div>
        @endforeach
        </div>
       </div>
     @endforeach
    </div>
    <div class="pricing-overlay-bg" data-background="{{ frontendAsset('images/bg/pricing-overlay-bg.png') }}"></div>
    <div class="pricing-dot-bg" data-background="{{ frontendAsset('images/bg/pricing-dot.png') }}"></div>

  </div>