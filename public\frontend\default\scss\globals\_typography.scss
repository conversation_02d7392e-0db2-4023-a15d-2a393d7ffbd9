@use '../utils' as *;

/*----------------------------------------*/
/* Typography css
/*----------------------------------------*/
* {
	margin: 0;
	padding: 0;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

body {
	font-family: var(--td-ff-body);
	font-size: 16px;
	font-weight: 400;
	line-height: 1.5;
	color: var(--td-text-primary);
	background-color: var(--td-white);

	&.landing-page-one {
		background: var(--td-void);
	}
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: var(--td-ff-heading);
	color: var(--td-heading);
	margin-top: 0px;
	line-height: 1.3;
	margin-bottom: 0;
	word-break: break-word;
}

h1,
.h1 {
	font-size: clamp(2.5rem, 4vw + 1rem, 2.986rem);
	/* ~47.78pt */
	line-height: 1.2;
	font-weight: 700;
}

h2,
.h2 {
	font-size: clamp(2rem, 3.5vw + 0.8rem, 2.488rem);
	/* ~38.22pt */
	line-height: 1.2;
	font-weight: 700;
}

h3,
.h3 {
	font-size: clamp(1.75rem, 3vw + 0.5rem, 2.074rem);
	/* ~30.56pt */
	line-height: 1.3;
	font-weight: 700;
}

h4,
.h4 {
	font-size: clamp(1.5rem, 2.5vw + 0.3rem, 1.728rem);
	/* ~24.44pt */
	line-height: 1.4;
	font-weight: 600;
}

h5,
.h5 {
	font-size: clamp(1.25rem, 2vw + 0.2rem, 1.44rem);
	/* ~19.56pt */
	line-height: 1.5;
	font-weight: 600;
}

h6,
.h6 {
	font-size: clamp(1.1rem, 1.5vw + 0.1rem, 1.2rem);
	/* ~16pt */
	line-height: 1.56;
	font-weight: 500;
}

ul {
	margin: 0px;
	padding: 0px;
}

p {
	font-size: rem(16);
	line-height: 1.5;

	&.b1 {
		font-size: clamp(0.8125rem, 0.25vw + 0.7rem, 0.875rem);
		/* 13px–14px */
		line-height: 1.4;
	}

	&.b2 {
		font-size: clamp(0.875rem, 0.4vw + 0.7rem, 1rem);
		/* 14px–16px */
		line-height: 1.5;
	}

	&.b3 {
		font-size: clamp(1rem, 0.5vw + 0.8rem, 1.125rem);
		/* 16px–18px */
		line-height: 1.625;
	}

	&.b4 {
		font-size: clamp(1.125rem, 0.6vw + 0.8rem, 1.25rem);
		/* 18px–20px */
		line-height: 1.754;
	}

	&:last-child {
		margin-bottom: 0px;
	}
}

a {
	text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
	@include td-transition;
}

a:focus,
.button:focus {
	text-decoration: none;
	outline: none;
}

a:focus,
a:hover {
	text-decoration: none;
	color: inherit;
}

a,
button {
	color: inherit;
	outline: none;
	border: none;
	background: transparent;
}

img {
	max-width: 100%;
	object-fit: cover;
}

button:hover {
	cursor: pointer;
}

button:focus {
	outline: 0;
}

.uppercase {
	text-transform: uppercase;
}

.capitalize {
	text-transform: capitalize;
}

hr:not([size]) {
	border-color: rgba($black, $alpha: .1);
	opacity: 1;
	border-width: 1px;
}

*::-moz-selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

::-moz-selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

::selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

*::-moz-placeholder {
	opacity: 1;
	font-size: 14px;
}

*::placeholder {
	opacity: 1;
	font-size: 14px;
	font-weight: 400;
}