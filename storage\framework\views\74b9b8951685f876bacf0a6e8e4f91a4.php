<!-- Recent transaction section start -->
<?php use \App\Models\Transaction; ?>
<?php use \App\Enums\TxnType; ?>
<?php use \App\Enums\TxnStatus; ?>

<?php
    $payments = Transaction::whereIn('type',[TxnType::PlanPurchase])->latest()->take(5)->get();
    $withdraw = Transaction::whereIn('type',[TxnType::Withdraw, TxnType::WithdrawAuto])->with(['wallet.coin'])->latest()->take(5)->get();
?>
<section class="td-recent-transaction-area p-relative zi-11 section_space include-bg section_space-bottom">
    <div class="container">
       <div class="row justify-content-center">
          <div class="col-xxl-6 col-xl-6 col-lg-6">
             <div class="section-title-wrapper text-center section_title_space">
                <h2 class="section-title has_text_move_anim mb-15"><?php echo highlightColor($data['hero_title']); ?></h2>
                <p class="description has_fade_anim"><?php echo e($data['sub_title']); ?></p>
             </div>
          </div>
       </div>
       <div class="row gy-30 justify-content-center">
          <div class="col-xxl-6 col-xl-6 col-lg-6">
             <div class="recent-transactions-box has_fade_anim" data-fade-from="left">
                <div class="clip-path">
                   <div class="clip-path-inner">
                      <div class="transactions-heading mb-14">
                         <h4 class="title"><?php echo e(__('Latest Payment')); ?></h4>
                      </div>
                      <div class="transaction-contents table-responsive">
                         <div class="td-custom-table">
                            <div class="contents">
                               <div class="site-table-list site-table-head" data-background="<?php echo e(frontendAsset('images/bg/table-head.png')); ?>">
                                  <div class="site-table-col"><?php echo e(__('User')); ?></div>
                                  <div class="site-table-col"><?php echo e(__('Amount')); ?></div>
                               </div>
                               <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                               <div class="site-table-list">
                                  <div class="site-table-col">
                                     <span class="fw-7"><?php echo e($payment->user->full_name); ?></span>
                                  </div>
                                  <div class="site-table-col">
                                    <div class="right-two-grid">
                                        <span class="td-badge badge-outline-<?php echo e($payment->status->value == TxnStatus::Success->value ? 'success' : 'warning'); ?>"><?php echo e($payment->status); ?></span>
                                        <span class="fw-7"><?php echo e(formatAmount($payment->amount, $payment->wallet?->coin, true)); ?></span>
                                    </div>
                                   </div>
                               </div>
                               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </div>
                         </div>
                      </div>
                   </div>
                </div>
                <div class="bg-shape" data-background="<?php echo e(frontendAsset('images/bg/recent-dash-bg.png')); ?>">
                </div>
             </div>
          </div>
          <div class="col-xxl-6 col-xl-6 col-lg-6">
             <div class="recent-transactions-box has_fade_anim" data-fade-from="right">
                <div class="clip-path">
                   <div class="clip-path-inner">
                      <div class="transactions-heading mb-14">
                         <h4 class="title"><?php echo e(__('Latest Withdraw')); ?></h4>
                      </div>
                      <div class="transaction-contents table-responsive">
                         <div class="td-custom-table">
                            <div class="contents">
                                <div class="site-table-list site-table-head" data-background="<?php echo e(frontendAsset('images/bg/table-head.png')); ?>">
                                   <div class="site-table-col"><?php echo e(__('User')); ?></div>
                                   <div class="site-table-col"><?php echo e(__('Amount')); ?></div>
                                </div>
                                <?php $__currentLoopData = $withdraw; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="site-table-list">
                                   <div class="site-table-col">
                                      <span class="fw-7"><?php echo e($payment->user->full_name); ?></span>
                                   </div>
                                   <div class="site-table-col">
                                    <div class="right-two-grid">
                                        <span class="td-badge badge-outline-<?php echo e($payment->status->value == TxnStatus::Success->value ? 'success' : 'warning'); ?>"><?php echo e($payment->status); ?></span>
                                        <span class="fw-7"><?php echo e(formatAmount($payment->amount, $payment->wallet?->coin, true)); ?></span>
                                    </div>
                                   </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
 
                             </div>
                         </div>
                      </div>
                   </div>
                </div>
                <div class="bg-shape" data-background="<?php echo e(frontendAsset('images/bg/recent-dash-bg.png')); ?>">
                </div>
             </div>
          </div>
       </div>
    </div>
    <div class="recent-transition-bg" data-background="<?php echo e(frontendAsset('images/bg/recent-transaction-bg.png')); ?>"></div>

 </section>
 <!-- Recent transaction section end --><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/home/<USER>/__payment-record.blade.php ENDPATH**/ ?>