<?php $__env->startSection('style'); ?>
    <style>
        .apply-btn-filter {
            display: inline-block;
            background: #5e3fc9 !important;
            border-radius: 3px;
            padding: 8px 20px;
            border: none;
            color: #ffffff !important;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            min-width: 116px;
            margin-top: 10px;
        }
    </style>
<?php $__env->stopSection(); ?>
<form action="<?php echo e(request()->url()); ?>" method="get" id="filterForm">
    <div class="table-filter">
        <div class="filter">
            <div class="search">
                <input type="text" id="search" name="search" value="<?php echo e(request('search')); ?>"
                    placeholder="Search..." />
            </div>
            <button type="submit" class="apply-btn"><i data-lucide="search"></i>Search</button>
        </div>
        <div class="filter d-flex">
            <select class="form-select form-select-sm show" aria-label=".form-select-sm example" name="perPage"
                id="perPage">
                <option value="15" <?php echo e(request('perPage') == 15 ? 'selected' : ''); ?>>15</option>
                <option value="30" <?php echo e(request('perPage') == 30 ? 'selected' : ''); ?>>30</option>
                <option value="45" <?php echo e(request('perPage') == 45 ? 'selected' : ''); ?>>45</option>
                <option value="60" <?php echo e(request('perPage') == 60 ? 'selected' : ''); ?>>60</option>
            </select>
            <?php if(isset($status) && $status == true): ?>
                <select class="form-select form-select-sm" aria-label=".form-select-sm example" name="status"
                    id="status">
                    <option value="" disabled selected><?php echo e(__('Status')); ?></option>
                    <option value="all" <?php if(request('status') == 'all'): echo 'selected'; endif; ?>><?php echo e(__('All')); ?></option>
                    <option value="success" <?php if(request('status') == 'success'): echo 'selected'; endif; ?>><?php echo e(__('Success')); ?></option>
                    <option value="pending" <?php if(request('status') == 'pending'): echo 'selected'; endif; ?>><?php echo e(__('Pending')); ?></option>
                    <option value="failed" <?php if(request('status') == 'failed'): echo 'selected'; endif; ?>><?php echo e(__('Failed')); ?></option>
                </select>
            <?php endif; ?>
            <?php if(isset($type) && $type == true): ?>
                <select class="form-select form-select-sm" aria-label=".form-select-sm example" name="type"
                    id="type">
                    <option value="" disabled selected><?php echo e(__('Type')); ?></option>
                    <option value="all" <?php if(request('type') == 'all'): echo 'selected'; endif; ?>><?php echo e(__('All')); ?></option>
                    <?php $__currentLoopData = \App\Enums\TxnType::cases(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($type->value); ?>" <?php if(trim(request('type')) === trim($type->value)): echo 'selected'; endif; ?>><?php echo e($type->value); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            <?php endif; ?>
        </div>
    </div>
</form>
<?php $__env->startPush('single-script'); ?>
    <script>
        (function($) {
            "use strict";
            $('#perPage').on('change', function() {
                $('#filterForm').submit();
            });

            $('#order').on('change', function() {
                $('#filterForm').submit();
            });

            $('#status').on('change', function() {
                $('#filterForm').submit();
            });

            $('#type').on('change', function() {
                $('#filterForm').submit();
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/transaction/include/__filter.blade.php ENDPATH**/ ?>