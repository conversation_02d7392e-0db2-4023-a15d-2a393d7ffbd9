<?php $__env->startSection('title'); ?>
    <?php echo e(__('Faq Section')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-xl-12">
                        <div class="title-content">
                            <h2 class="title"><?php echo $__env->yieldContent('title'); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php echo $__env->make('backend.page.default.include.__language_bar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <div class="tab-content" id="pills-tabContent">

            <?php $__currentLoopData = $groupData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $data = new Illuminate\Support\Fluent($value);
                ?>

                <div class="tab-pane fade <?php echo e($loop->index == 0 ? 'show active' : ''); ?>" id="<?php echo e($key); ?>"
                    role="tabpanel" aria-labelledby="pills-informations-tab">
                    <div class="site-card">
                        <div class="site-card-header">
                            <h3 class="title"><?php echo e(__('Contents')); ?></h3>
                        </div>
                        <div class="site-card-body">
                            <form action="<?php echo e(route('admin.page.section.section.update')); ?>" method="post"
                                enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="section_code" value="faqs">
                                <input type="hidden" name="section_locale" value="<?php echo e($key); ?>">
                                <?php if($key == 'en'): ?>
                                    <div class="site-input-groups row">
                                        <label for=""
                                            class="col-sm-3 col-label pt-0"><?php echo e(__('Section Visibility')); ?><i
                                                data-lucide="info" data-bs-toggle="tooltip" title=""
                                                data-bs-original-title="Manage Section Visibility"></i></label>
                                        <div class="col-sm-3">
                                            <div class="site-input-groups">
                                                <div class="switch-field">
                                                    <input type="radio" id="active" name="status"
                                                        <?php if($status): ?> checked <?php endif; ?> value="1" />
                                                    <label for="active"><?php echo e(__('Show')); ?></label>
                                                    <input type="radio" id="deactivate" name="status"
                                                        <?php if(!$status): ?> checked <?php endif; ?> value="0" />
                                                    <label for="deactivate"><?php echo e(__('Hide')); ?></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="site-input-groups row">
                                    <label for="" class="col-sm-3 col-label"><?php echo e(__('Faq Title')); ?></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="faq_title" class="box-input"
                                            value="<?php echo e($data->faq_title); ?>">
                                                                                    <small class="text-muted">
                                            <?php echo e(__('Use this shortcode to highlight words. Example: [[color_text= Your Text Here ]]')); ?>

                                        </small>

                                    </div>
                                </div>
                                <div class="site-input-groups row">
                                    <label for="" class="col-sm-3 col-label"><?php echo e(__('Faq Subtitle')); ?></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="faq_subtitle" class="box-input"
                                            value="<?php echo e($data->faq_subtitle); ?>">
                                    </div>
                                </div>
                                <?php if($key == 'en'): ?>
                                    <div class="site-input-groups row">
                                        <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-label">
                                            <?php echo e(__('Image')); ?>

                                        </div>
                                        <div class="col-xl-9 col-lg-9 col-md-9 col-sm-12">
                                            <div class="wrap-custom-file">
                                                <input type="file" name="image" id="image"
                                                    accept=".gif, .jpg, .png, .svg, .webp" />
                                                <label for="image" id="image"
                                                    <?php if($data->image): ?> class="file-ok"
                                                   style="background-image: url(<?php echo e(asset($data->image)); ?>)" <?php endif; ?>>
                                                    <img class="upload-icon"
                                                        src="<?php echo e(asset('global/materials/upload.svg')); ?>" alt="" />
                                                    <span><?php echo e(__('Update Image')); ?></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="row">
                                    <div class="offset-sm-3 col-sm-9">
                                        <button type="submit"
                                            class="site-btn-sm primary-btn w-100"><?php echo e(__('Save Changes')); ?></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div class="site-card">
            <div class="site-card-header">
                <h3 class="title"><?php echo e(__('Contents')); ?></h3>
                <div class="card-header-links">
                    <a href="" class="card-header-link" type="button" data-bs-toggle="modal"
                        data-bs-target="#addNew"><?php echo e(__('Add New')); ?></a>
                </div>
            </div>
            <div class="site-card-body">
                <div class="site-table table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col"><?php echo e(__('Question')); ?></th>
                                <th scope="col"><?php echo e(__('Answer')); ?></th>
                                <th scope="col"><?php echo e(__('Action')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $landingContent; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($content->title); ?></td>
                                    <td><?php echo e($content->description); ?></td>
                                    <td>
                                        <button class="round-icon-btn primary-btn editContent" type="button"
                                            data-id="<?php echo e($content->id); ?>">
                                            <i data-lucide="edit-3"></i>
                                        </button>
                                        <button class="round-icon-btn red-btn deleteContent" type="button"
                                            data-id="<?php echo e($content->id); ?>">
                                            <i data-lucide="trash-2"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Add New  -->
    <?php echo $__env->make('backend.page.default.section.include.__add_new_faq', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Add New End -->

    <!-- Modal for Edit -->
    <?php echo $__env->make('backend.page.default.section.include.__edit', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Edit  End-->

    <!-- Modal for Delete  -->
    <?php echo $__env->make('backend.page.default.section.include.__delete', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Delete  End-->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        $('.editContent').on('click', function(e) {
            "use strict";
            e.preventDefault();
            var id = $(this).data('id');

            var url = '<?php echo e(route('admin.page.content-edit', ':id')); ?>';
            url = url.replace(':id', id);

            $.ajax({
                url: url,
                type: 'GET',
                success: function(response) {
                    // Handle the response HTML
                    $('#target-element').html(response.html);
                    $('#editContent').modal('show');
                },
                error: function(xhr) {
                    // Handle any errors that occurred during the request
                    console.log(xhr.responseText);
                }
            });
        });

        $('.deleteContent').on('click', function(e) {
            "use strict";
            e.preventDefault();
            var id = $(this).data('id');
            $('#deleteId').val(id);
            $('#deleteContent').modal('show');
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/page/default/section/faqs.blade.php ENDPATH**/ ?>