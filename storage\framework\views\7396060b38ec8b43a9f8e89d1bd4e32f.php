<div class="modal fade" id="addNew" tabindex="-1" aria-labelledby="addNewAboutUsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content site-table-modal">
            <div class="modal-body popup-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
                <div class="popup-body-text">
                    <h3 class="title mb-4"><?php echo e(__('Add New')); ?></h3>
                    <form action="<?php echo e(route('admin.page.content-store')); ?>" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="type" value="mining-solutions">

                        <div class="site-input-groups">
                            <label for="" class="box-input-label"><?php echo e(__('Icon')); ?></label>
                            <div class="wrap-custom-file">
                                <input type="file" name="icon" id="icon"
                                    accept=".gif, .jpg, .png, .svg, .webp" />
                                <label for="icon">
                                    <img class="upload-icon" src="<?php echo e(asset('global/materials/upload.svg')); ?>"
                                        alt="" />
                                    <span><?php echo e(__('Upload')); ?></span>
                                </label>
                            </div>
                        </div>

                        <div class="site-input-groups">
                            <label for="" class="box-input-label"><?php echo e(__('Title:')); ?></label>
                            <input type="text" name="title" class="box-input mb-0"
                                placeholder="<?php echo e(__('Title')); ?>" required="" />
                        </div>

                        <div class="site-input-groups mb-0">
                            <label for="" class="box-input-label"><?php echo e(__('Description:')); ?></label>
                            <textarea name="description" class="form-textarea" placeholder="<?php echo e(__('Description')); ?>"></textarea>
                        </div>

                        <div class="action-btns">
                            <button type="submit" class="site-btn-sm primary-btn me-2">
                                <i data-lucide="check"></i>
                                <?php echo e(__('Add New')); ?>

                            </button>
                            <a href="#" class="site-btn-sm red-btn" data-bs-dismiss="modal" aria-label="Close">
                                <i data-lucide="x"></i>
                                <?php echo e(__('Close')); ?>

                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/page/default/section/include/__add_new_mining-solutions.blade.php ENDPATH**/ ?>