@if ($kyc->status == 'approved')
    <ul>
        <li>
            <span class="text">
                {{ __('Status:') }}
            </span>
            <span class="td-badge badge-success">
                {{ __('Approved') }}
            </span>
        </li>
    </ul>
@elseif($kyc->status == 'rejected')
    <ul>
        <li>
            <span class="text">
                {{ __('Status:') }}
            </span>
            <span class="td-badge badge-danger">
                {{ __('Rejected') }}
            </span>
        </li>
    </ul>
@elseif($kyc->status == 'pending')
    <ul>
        <li>
            <span class="text">
                {{ __('Status:') }}
            </span>
            <span class="td-badge badge-warning">
                {{ __('Pending') }}
            </span>
        </li>
    </ul>
@endif
<ul>
    <li>
        <span class="text">
            {{ __('Submission Date:') }}
        </span>
        <span class="text-highlight">
            {{ date('d M Y h:i A', strtotime($kyc->created_at)) }}
        </span>
    </li>
</ul>

@if ($kyc->message)
<ul>
    <li>
        <span class="text">
            {{ __('Message from admin:') }}
        </span>
        <span class="text-highlight">
            {{ $kyc->message }}
        </span>
    </li>
</ul>
@endif

@foreach ($kyc->data as $key => $value)
    <ul>
        <li>
            <span class="text">{{ $key }}:</span>
            <span class="text-highlight">
                @if (is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|bmp)$/i', $value))
                    <a href="{{ asset($value) }}" target="_blank" rel="noopener noreferrer">{{ __('Click to view') }}</a>
                @else
                    {{ $value }}
                @endif
            </span>
        </li>
    </ul>
@endforeach