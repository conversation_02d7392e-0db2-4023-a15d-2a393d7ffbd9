<?php $__env->startSection('title'); ?>
    <?php echo e(__('Ticket Create')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="col-xxl-12">
    <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
           <h3 class="page-title"><?php echo e(__('Support Tickets')); ?></h3>
        </div>
     </div>
     <?php echo $__env->make('frontend::user.ticket.include.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<div class="col-xxl-12">
    <div class="add-money-area default-area-style">
        <div class="heading-top">
            <h5 class="title"><?php echo e(__('Add New Ticket')); ?></h5>
        </div>
        <div class="default-content-inner">
            <div class="add-money-form">
                <form action="<?php echo e(route('user.ticket.store')); ?>" method="post" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="row gy-24">
                
                        <div class="col-lg-12">
                            <div class="td-form-group">
                                <label class="input-label" for="title"><?php echo e(__('Title')); ?><span>*</span></label>
                                <div class="input-field">
                                    <input type="text" name="title" id="title" class="form-control" required>
                                </div>
                                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                
                        <div class="col-lg-12">
                            <div class="td-form-group">
                                <label class="input-label" for="description"><?php echo e(__('Description')); ?><span>*</span></label>
                                <div class="input-field">
                                    <textarea name="message" id="description" class="form-control" required></textarea>
                                </div>
                                <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                
                        <div class="col-lg-12">
                            <div class="td-form-group">
                                <div class="d-flex align-items-center justify-content-between gap-3 mb-2">
                                    <label class="input-label mb-0" for="attach"><?php echo e(__('Attachments')); ?></label>
                                    <button id="addBtn" type="button" class="td-btn btn-xs btn-chip grd-fill-btn-primary">
                                        <?php echo e(__('Add Attach')); ?>

                                    </button>
                                </div>
                                <div class="input-field">
                                    <div id="uploadItems">
                                        <div class="upload-custom-file">
                                            <input type="file" name="attach[]" class="upload-input" accept=".gif, .jpg, .png, .jpeg, .webp, .pdf, .svg" id="image">
                                            <label for="image">
                                                <img class="upload-icon" src="<?php echo e(frontendAsset('images/icons/upload.svg')); ?>" alt="upload file">
                                                <span><b><?php echo e(__('Attach File')); ?></b></span>
                                            </label>
                                            <button type="button" class="file-upload-close" style="display: none;">
                                                <i><iconify-icon icon="tabler:circle-x"></iconify-icon></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <?php $__errorArgs = ['attach.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                
                        <div class="col-lg-12">
                            <div class="td-form-btns d-flex justify-content-end mt-30">
                                <button type="submit" class="td-btn btn-chip grd-fill-btn-primary">
                                    <?php echo e(__('Create Ticket')); ?>

                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        "use strict";
        $(document).ready(function() {
            function handleFileInputChange(event) {
                var $file = $(this),
                    $label = $file.closest('label'),
                    $labelText = $label.find('span').first(),
                    labelDefault = $labelText.text();

                var fileName = $file.val().split('\\').pop(),
                    tmppath = URL.createObjectURL(event.target.files[0]);

                if (fileName) {
                    $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                    $labelText.text(fileName);
                } else {
                    $label.removeClass('file-ok');
                    $labelText.text(labelDefault);
                }
            }

            function handleFileUploadClose() {
                $(this).closest('.upload-custom-file').remove();
            }

            function addUploadItem() {
                var uniqueId = 'image' + ($('.upload-custom-file').length + 1);

                var uploadItem = $(`
                <div class="upload-custom-file">
                    <input type="file" name="attach[]" class="upload-input"
                        accept=".gif, .jpg, .png, .jpeg, .webp, .pdf, .svg" id="${uniqueId}">
                    <label for="${uniqueId}">
                        <img class="upload-icon"
                            src="<?php echo e(frontendAsset('images/icons/upload.svg')); ?>"
                            alt="upload file">
                        <span>
                            <b>
                                <?php echo e(__('Attach File')); ?>

                            </b>
                        </span>
                    </label>
                    <button type="button" class="file-upload-close" style="display: none;">
                        <svg width="8" height="8" viewBox="0 0 8 8" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.62796 1.37204C6.79068 1.53476 6.79068 1.79858 6.62796 1.96129L1.96129 6.62796C1.79858 6.79068 1.53476 6.79068 1.37204 6.62796C1.20932 6.46524 1.20932 6.20142 1.37204 6.03871L6.03871 1.37204C6.20142 1.20932 6.46524 1.20932 6.62796 1.37204Z"
                                fill="white" />
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M1.37204 1.37204C1.53476 1.20932 1.79858 1.20932 1.96129 1.37204L6.62796 6.03871C6.79068 6.20142 6.79068 6.46524 6.62796 6.62796C6.46524 6.79068 6.20142 6.79068 6.03871 6.62796L1.37204 1.96129C1.20932 1.79858 1.20932 1.53476 1.37204 1.37204Z"
                                fill="white" />
                        </svg>
                    </button>
                </div>
            `);

                uploadItem.find('.upload-input').on('change', handleFileInputChange);

                uploadItem.find('.file-upload-close').on('click', handleFileUploadClose);

                $('#uploadItems').append(uploadItem);
            }

            $('#addBtn').on('click', addUploadItem);

            $(document).on('change', '.upload-input', handleFileInputChange);

            $(document).on('click', '.file-upload-close', handleFileUploadClose);
        });
    </script>
    <script>
        $(document).on('change', 'input[type="file"]', function(event) {
            var $file = $(this),
                $label = $file.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            var fileName = $file.val().split('\\').pop(),
                file = event.target.files[0],
                fileType = file ? file.type.split('/')[0] : null,
                tmppath = file ? URL.createObjectURL(file) : null;

            if (fileName) {
                if (fileType === "image") {

                    $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                } else {

                    $label.addClass('file-ok').css('background-image', 'none');
                }
                $labelText.text(fileName);
                $typeFileText.hide();
                $label.siblings('.file-upload-close').show();
            } else {
                resetUpload($file, $label, $labelText, $typeFileText,
                    labelDefault);
            }
        });

        $(document).on('click', '.file-upload-close', function() {
            var $button = $(this),
                $uploadWrapper = $button.closest('.upload-custom-file'),
                $fileInput = $uploadWrapper.find('input[type="file"]'),
                $label = $fileInput.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
        });

        function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
            $fileInput.val('');
            $label.removeClass('file-ok').css('background-image', 'none');
            $labelText.text(labelDefault);
            $typeFileText.show();
            $label.siblings('.file-upload-close').hide();
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/ticket/create.blade.php ENDPATH**/ ?>