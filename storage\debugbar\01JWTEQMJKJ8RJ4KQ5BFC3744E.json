{"__meta": {"id": "01JWTEQMJKJ8RJ4KQ5BFC3744E", "datetime": "2025-06-03 14:32:39", "utime": **********.507852, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[14:32:38] LOG.error: No hint path defined for [frontend]. {\n    \"userId\": 1,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.692653, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1748939542.721273, "end": **********.507872, "duration": 16.786599159240723, "duration_str": "16.79s", "measures": [{"label": "Booting", "start": 1748939542.721273, "relative_start": 0, "end": **********.56593, "relative_end": **********.56593, "duration": 15.***************, "duration_str": "15.84s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.565944, "relative_start": 15.**************, "end": **********.507878, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "942ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.582731, "relative_start": 15.**************, "end": **********.585515, "relative_end": **********.585515, "duration": 0.0027840137481689453, "duration_str": "2.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.820189, "relative_start": 16.***************, "end": **********.820189, "relative_end": **********.820189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.83314, "relative_start": 16.**************, "end": **********.83314, "relative_end": **********.83314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.842386, "relative_start": 16.121113061904907, "end": **********.842386, "relative_end": **********.842386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.851052, "relative_start": 16.12977910041809, "end": **********.851052, "relative_end": **********.851052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.861198, "relative_start": 16.139925003051758, "end": **********.861198, "relative_end": **********.861198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.869124, "relative_start": 16.14785099029541, "end": **********.869124, "relative_end": **********.869124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.877633, "relative_start": 16.156360149383545, "end": **********.877633, "relative_end": **********.877633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.88516, "relative_start": 16.16388702392578, "end": **********.88516, "relative_end": **********.88516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.893854, "relative_start": 16.17258095741272, "end": **********.893854, "relative_end": **********.893854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.904064, "relative_start": 16.182790994644165, "end": **********.904064, "relative_end": **********.904064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.914855, "relative_start": 16.19358205795288, "end": **********.914855, "relative_end": **********.914855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.366324, "relative_start": 16.64505100250244, "end": **********.366324, "relative_end": **********.366324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.374911, "relative_start": 16.653638124465942, "end": **********.374911, "relative_end": **********.374911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.382858, "relative_start": 16.661585092544556, "end": **********.382858, "relative_end": **********.382858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.390513, "relative_start": 16.66923999786377, "end": **********.390513, "relative_end": **********.390513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.39745, "relative_start": 16.67617702484131, "end": **********.39745, "relative_end": **********.39745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.421834, "relative_start": 16.700561046600342, "end": **********.421834, "relative_end": **********.421834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.46584, "relative_start": 16.744567155838013, "end": **********.46584, "relative_end": **********.46584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.473241, "relative_start": 16.751968145370483, "end": **********.473241, "relative_end": **********.473241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.480974, "relative_start": 16.759701013565063, "end": **********.480974, "relative_end": **********.480974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.489131, "relative_start": 16.767858028411865, "end": **********.489131, "relative_end": **********.489131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.496302, "relative_start": 16.775028944015503, "end": **********.496302, "relative_end": **********.496302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 36515608, "peak_usage_str": "35MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "InvalidArgumentException", "message": "No hint path defined for [frontend].", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php", "line": 111, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:69</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>89</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"22 characters\">parseNamespaceSegments</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\View\\FileViewFinder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">frontend::home.index</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">findNamespacedView</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\View\\FileViewFinder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">frontend::home.index</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/laravel/framework/src/Illuminate/View/Factory.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>150</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">find</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\View\\FileViewFinder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">frontend::home.index</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1079</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Illuminate\\View\\Factory</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">frontend::home.index</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>homeContent</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22050 title=\"2 occurrences\">#2050</a><samp data-depth=5 id=sf-dump-*********-ref22050 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#1974</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Hero Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">hero</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"1062 characters\">{&quot;hero_title&quot;:&quot;Powering Your Profits with Next-Gen [[color_text= Mining ]]&quot;,&quot;hero_description&quot;:&quot;Innovative Mining Solutions for a Sustainable Future.Innovative Mining Solutions for a Sustainable Future.Innovativ&quot;,&quot;bonus_text&quot;:&quot;Sign Up now &amp; get up to $5000&quot;,&quot;bubble_text&quot;:&quot;Our client satisfy&quot;,&quot;bubble_counter&quot;:&quot;80+&quot;,&quot;user_photos&quot;:&quot;[\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/9Ss9it1vd3siHXWpAavF.webp\\&quot;,\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/VPte0717hGisVP6kuYjB.webp\\&quot;,\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/GJChfQzQtoqC4vXXgMrJ.webp\\&quot;,\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/fdE6x693e32c7dIqsdcu.webp\\&quot;,\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/LNELt5577nubXEmHNqiT.webp\\&quot;]&quot;,&quot;hero_subtitle&quot;:&quot;Sign Up now &amp; get up to $5,000&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/whyFsUktRePHqgp7GM7e.webp&quot;,&quot;button_label&quot;:&quot;Start Mining&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;right_button_label&quot;:&quot;Explore Plans&quot;,&quot;right_button_url&quot;:&quot;\\/register&quot;,&quot;right_button_target&quot;:&quot;_self&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 15:06:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Hero Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">hero</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"1062 characters\">{&quot;hero_title&quot;:&quot;Powering Your Profits with Next-Gen [[color_text= Mining ]]&quot;,&quot;hero_description&quot;:&quot;Innovative Mining Solutions for a Sustainable Future.Innovative Mining Solutions for a Sustainable Future.Innovativ&quot;,&quot;bonus_text&quot;:&quot;Sign Up now &amp; get up to $5000&quot;,&quot;bubble_text&quot;:&quot;Our client satisfy&quot;,&quot;bubble_counter&quot;:&quot;80+&quot;,&quot;user_photos&quot;:&quot;[\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/9Ss9it1vd3siHXWpAavF.webp\\&quot;,\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/VPte0717hGisVP6kuYjB.webp\\&quot;,\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/GJChfQzQtoqC4vXXgMrJ.webp\\&quot;,\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/fdE6x693e32c7dIqsdcu.webp\\&quot;,\\&quot;global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/LNELt5577nubXEmHNqiT.webp\\&quot;]&quot;,&quot;hero_subtitle&quot;:&quot;Sign Up now &amp; get up to $5,000&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/whyFsUktRePHqgp7GM7e.webp&quot;,&quot;button_label&quot;:&quot;Start Mining&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;right_button_label&quot;:&quot;Explore Plans&quot;,&quot;right_button_url&quot;:&quot;\\/register&quot;,&quot;right_button_target&quot;:&quot;_self&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 15:06:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1966</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2048</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>14</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">How It Operate Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"81 characters\">{&quot;title&quot;:&quot;How It [[color_text= Operate]]&quot;,&quot;subtitle&quot;:&quot;Discover how it all works&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>2</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>14</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">How It Operate Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"81 characters\">{&quot;title&quot;:&quot;How It [[color_text= Operate]]&quot;,&quot;subtitle&quot;:&quot;Discover how it all works&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>2</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2052</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2075</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>181</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Set up your account</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>170</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:05:50</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:05:50</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>181</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Set up your account</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>170</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:05:50</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:05:50</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2074</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>182</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Set up your account</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>182</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:05:57</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:17:17</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>182</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Set up your account</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>182</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:05:57</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:17:17</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2073</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>183</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Set up your account</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>183</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:06:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:21:28</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>183</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Set up your account</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>183</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:06:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:21:28</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2072</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>184</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Set up your account</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>184</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:06:12</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:06:12</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>184</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Set up your account</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">how-it-operate</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>184</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:06:12</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-30 18:06:12</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2047</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>16</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Scheme Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">scheme</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"835 characters\">{&quot;title&quot;:&quot;How It [[color_text= Works]]&quot;,&quot;subtitle&quot;:&quot;Discover how it all works&quot;,&quot;hero_title&quot;:&quot;[[color_text= Flexible Mining]] Solutions&quot;,&quot;sub_title&quot;:&quot;Choose the right plan for efficient and cost-effective mining operations.&quot;,&quot;hero_description&quot;:&quot;We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.&quot;,&quot;cta_title&quot;:&quot;324 % staking rewards&quot;,&quot;cta_description&quot;:&quot;Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!&quot;,&quot;button_label&quot;:&quot;Learn more&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 09:07:11</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:25:01</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>16</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Scheme Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">scheme</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"835 characters\">{&quot;title&quot;:&quot;How It [[color_text= Works]]&quot;,&quot;subtitle&quot;:&quot;Discover how it all works&quot;,&quot;hero_title&quot;:&quot;[[color_text= Flexible Mining]] Solutions&quot;,&quot;sub_title&quot;:&quot;Choose the right plan for efficient and cost-effective mining operations.&quot;,&quot;hero_description&quot;:&quot;We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.&quot;,&quot;cta_title&quot;:&quot;324 % staking rewards&quot;,&quot;cta_description&quot;:&quot;Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!&quot;,&quot;button_label&quot;:&quot;Learn more&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 09:07:11</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:25:01</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2014</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2046</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Earning Calculation Section<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"19 characters\">earning-calculation</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"158 characters\">{&quot;title&quot;:&quot;[[color_text= Calculate ]] your possible Earnings&quot;,&quot;subtitle&quot;:&quot;Explore different outcomes based on your choices and see where the path could lead.&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2022-10-22 13:54:48</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:59:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Earning Calculation Section<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"19 characters\">earning-calculation</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"158 characters\">{&quot;title&quot;:&quot;[[color_text= Calculate ]] your possible Earnings&quot;,&quot;subtitle&quot;:&quot;Explore different outcomes based on your choices and see where the path could lead.&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2022-10-22 13:54:48</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:59:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2015</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2045</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>17</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Payment Record Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payment-record</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"828 characters\">{&quot;title&quot;:&quot;How It [[color_text= Works]]&quot;,&quot;subtitle&quot;:&quot;Discover how it all works&quot;,&quot;hero_title&quot;:&quot;[[color_text= Payment ]] Records&quot;,&quot;sub_title&quot;:&quot;Track all your mining transactions with complete transparency and security.&quot;,&quot;hero_description&quot;:&quot;We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.&quot;,&quot;cta_title&quot;:&quot;324 % staking rewards&quot;,&quot;cta_description&quot;:&quot;Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!&quot;,&quot;button_label&quot;:&quot;Learn more&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 09:07:11</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:24:25</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>17</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Payment Record Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">payment-record</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"828 characters\">{&quot;title&quot;:&quot;How It [[color_text= Works]]&quot;,&quot;subtitle&quot;:&quot;Discover how it all works&quot;,&quot;hero_title&quot;:&quot;[[color_text= Payment ]] Records&quot;,&quot;sub_title&quot;:&quot;Track all your mining transactions with complete transparency and security.&quot;,&quot;hero_description&quot;:&quot;We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.&quot;,&quot;cta_title&quot;:&quot;324 % staking rewards&quot;,&quot;cta_description&quot;:&quot;Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!&quot;,&quot;button_label&quot;:&quot;Learn more&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-24 09:07:11</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:24:25</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2012</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2044</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>18</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Mining Solutions Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"225 characters\">{&quot;solutions_title&quot;:&quot;Our Mining [[color_text= Solutions ]]&quot;,&quot;solutions_subtitle&quot;:&quot;A crypto wallet system offers secure storage, transaction management, private key protection, multi-currency support, and seamless integration&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>18</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Mining Solutions Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"225 characters\">{&quot;solutions_title&quot;:&quot;Our Mining [[color_text= Solutions ]]&quot;,&quot;solutions_subtitle&quot;:&quot;A crypto wallet system offers secure storage, transaction management, private key protection, multi-currency support, and seamless integration&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2053</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2082</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>163</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/cI9OJAqan52IBRJfZSIY.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Exploration &amp; Extraction</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">We are ready to answer all your questions and advise you 24/7.</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>163</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:25</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:25</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>163</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/cI9OJAqan52IBRJfZSIY.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Exploration &amp; Extraction</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">We are ready to answer all your questions and advise you 24/7.</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>163</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:25</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:25</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2081</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>164</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/UDUE3B3c9FrZdlcSpUq1.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Drilling &amp; Blasting</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">We are ready to answer all your questions and advise you 24/7.</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>164</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:40</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:35:56</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>164</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/UDUE3B3c9FrZdlcSpUq1.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Drilling &amp; Blasting</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">We are ready to answer all your questions and advise you 24/7.</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>164</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:40</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:35:56</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2080</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>165</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/G2XLFuuWK5ZUqAfTRCAD.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Mineral Processing</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">We are ready to answer all your questions and advise you 24/7.</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>165</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:55</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:55</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>165</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/G2XLFuuWK5ZUqAfTRCAD.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Mineral Processing</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">We are ready to answer all your questions and advise you 24/7.</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>165</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:55</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:54:55</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2079</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>166</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/3Yy7Az2vqTtTLxksA61T.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Environmental Sustainability</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">We are ready to answer all your questions and advise you 24/7.</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>166</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:55:07</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:37:06</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>166</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/3Yy7Az2vqTtTLxksA61T.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Environmental Sustainability</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">We are ready to answer all your questions and advise you 24/7.</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">mining-solutions</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>166</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 10:55:07</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 14:37:06</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2043</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Future Of Mining Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"17 characters\">feature-of-mining</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"858 characters\">{&quot;title&quot;:&quot;How It [[color_text= Works]]&quot;,&quot;subtitle&quot;:&quot;Discover how it all works&quot;,&quot;hero_title&quot;:&quot;[[color_text= Flexible Mining]] Solutions Tailored to Your Needs&quot;,&quot;sub_title&quot;:&quot;Choose the right plan for efficient and cost-effective mining operations.&quot;,&quot;hero_description&quot;:&quot;We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.&quot;,&quot;cta_title&quot;:&quot;324 % staking rewards&quot;,&quot;cta_description&quot;:&quot;Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!&quot;,&quot;button_label&quot;:&quot;Learn more&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Future Of Mining Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"17 characters\">feature-of-mining</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"858 characters\">{&quot;title&quot;:&quot;How It [[color_text= Works]]&quot;,&quot;subtitle&quot;:&quot;Discover how it all works&quot;,&quot;hero_title&quot;:&quot;[[color_text= Flexible Mining]] Solutions Tailored to Your Needs&quot;,&quot;sub_title&quot;:&quot;Choose the right plan for efficient and cost-effective mining operations.&quot;,&quot;hero_description&quot;:&quot;We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.&quot;,&quot;cta_title&quot;:&quot;324 % staking rewards&quot;,&quot;cta_description&quot;:&quot;Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!&quot;,&quot;button_label&quot;:&quot;Learn more&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2009</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2042</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Supported Currencies Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"20 characters\">supported-currencies</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"317 characters\">{&quot;hero_title&quot;:&quot;View and Manage your Assets Across all Supported [[color_text= Currencies ]]&quot;,&quot;button_label&quot;:&quot;Start Mining&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/xivYoTKThIXOmeCeqgd1.png&quot;,&quot;logo&quot;:&quot;global\\/uploads\\/landing-contents\\/logo\\/s9FstwjTN2qgMHVaSFck.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Supported Currencies Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"20 characters\">supported-currencies</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"317 characters\">{&quot;hero_title&quot;:&quot;View and Manage your Assets Across all Supported [[color_text= Currencies ]]&quot;,&quot;button_label&quot;:&quot;Start Mining&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/xivYoTKThIXOmeCeqgd1.png&quot;,&quot;logo&quot;:&quot;global\\/uploads\\/landing-contents\\/logo\\/s9FstwjTN2qgMHVaSFck.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2017</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2041</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>21</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Why Work With Us</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">why-work-with-us</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"287 characters\">{&quot;features_title&quot;:&quot;[[color_text= Why Work]] With Us&quot;,&quot;features_subtitle&quot;:&quot;A crypto wallet system offers secure storage, transaction management, private key protection, multi-currency support, and seamless integration&quot;,&quot;button_label&quot;:&quot;Read more&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>9</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>21</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Why Work With Us</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">why-work-with-us</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"287 characters\">{&quot;features_title&quot;:&quot;[[color_text= Why Work]] With Us&quot;,&quot;features_subtitle&quot;:&quot;A crypto wallet system offers secure storage, transaction management, private key protection, multi-currency support, and seamless integration&quot;,&quot;button_label&quot;:&quot;Read more&quot;,&quot;button_url&quot;:&quot;\\/&quot;,&quot;button_target&quot;:&quot;_self&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>9</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2018</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2078</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>167</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/gmm645zu83Pmm4gpcB5Y.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Advanced Technology</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"56 characters\">We are ready to answer all your questions and advise you</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">why-work-with-us</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>167</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:23</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:23</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>167</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/gmm645zu83Pmm4gpcB5Y.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Advanced Technology</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"56 characters\">We are ready to answer all your questions and advise you</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">why-work-with-us</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>167</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:23</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:23</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2077</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>168</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/SQ4u8OLpa4e7ij2RkKI4.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Sustainable Practices</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"56 characters\">We are ready to answer all your questions and advise you</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">why-work-with-us</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>168</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:35</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:35</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>168</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/SQ4u8OLpa4e7ij2RkKI4.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Sustainable Practices</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"56 characters\">We are ready to answer all your questions and advise you</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">why-work-with-us</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>168</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:35</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:35</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2076</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>169</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/DtLiZG7nASoNjChIZzQ8.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Experienced Team</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"56 characters\">We are ready to answer all your questions and advise you</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">why-work-with-us</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>169</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:49</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:49</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>169</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"46 characters\">global/uploads/images/DtLiZG7nASoNjChIZzQ8.png</span>\"\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Experienced Team</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"56 characters\">We are ready to answer all your questions and advise you</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">why-work-with-us</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>169</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:49</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-27 11:18:49</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2040</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>22</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">FAQ Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"342 characters\">{&quot;faq_title&quot;:&quot;Frequently Asked [[color_text=Questions]]&quot;,&quot;faq_subtitle&quot;:&quot;Frequently asked questions cover topics like wallet setup, security features, transaction processes, private key management, and multi-currency compatibility within the crypto wallet system.&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/DI3ZPukmzjjbejgPxuuL.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>10</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>22</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">FAQ Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"342 characters\">{&quot;faq_title&quot;:&quot;Frequently Asked [[color_text=Questions]]&quot;,&quot;faq_subtitle&quot;:&quot;Frequently asked questions cover topics like wallet setup, security features, transaction processes, private key management, and multi-currency compatibility within the crypto wallet system.&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/DI3ZPukmzjjbejgPxuuL.png&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>10</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2013</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2055</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>80</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"23 characters\">What is Cryptocurrency?</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"255 characters\">Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technolog</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>80</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-09 10:18:58</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-24 15:54:31</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>80</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"23 characters\">What is Cryptocurrency?</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"255 characters\">Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technolog</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>80</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-09 10:18:58</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-24 15:54:31</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2085</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>81</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"25 characters\">How can I track my order?</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"248 characters\">Cryptocurrency is a digital or virtual currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technology</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>81</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-09 10:19:33</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-24 15:54:31</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>81</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"25 characters\">How can I track my order?</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"248 characters\">Cryptocurrency is a digital or virtual currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technology</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>81</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-09 10:19:33</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-24 15:54:31</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2084</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>82</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I received the wrong item, what do I do?</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"256 characters\">Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technology</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>82</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-09 10:19:55</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-24 15:54:31</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>82</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I received the wrong item, what do I do?</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"256 characters\">Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technology</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>82</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-09 10:19:55</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-24 15:54:31</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                    <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingContent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingContent</span></span> {<a class=sf-dump-ref>#2083</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">landing_contents</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>83</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"24 characters\">How promote the product?</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"255 characters\">Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technolog</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>83</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-09 10:20:18</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-24 15:54:31</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>83</span>\n                        \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                        \"<span class=sf-dump-key>icon</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"24 characters\">How promote the product?</span>\"\n                        \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"255 characters\">Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technolog</span>\"\n                        \"<span class=sf-dump-key>photo</span>\" => <span class=sf-dump-const>null</span>\n                        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">faqs</span>\"\n                        \"<span class=sf-dump-key>locale_id</span>\" => <span class=sf-dump-num>83</span>\n                        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-09 10:20:18</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-24 15:54:31</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>10</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2039</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>23</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Testimonial Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"11 characters\">testimonial</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"201 characters\">{&quot;testimonial_title&quot;:&quot;Client [[color_text=Say]]&quot;,&quot;sub_title&quot;:&quot;Clients praise the crypto wallet system for its ease of use, robust security, seamless transactions, and reliable multi-currency support.&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-03-20 08:07:55</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>23</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Testimonial Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"11 characters\">testimonial</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"201 characters\">{&quot;testimonial_title&quot;:&quot;Client [[color_text=Say]]&quot;,&quot;sub_title&quot;:&quot;Clients praise the crypto wallet system for its ease of use, robust security, seamless transactions, and reliable multi-currency support.&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-03-20 08:07:55</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2010</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>11</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2038</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>24</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Blog Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">blog</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"120 characters\">{&quot;blog_title&quot;:&quot;Our [[color_text=Blogs]]&quot;,&quot;blog_subtitle&quot;:&quot;It is long established fact that a reader will be Distracted&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-03-20 08:07:55</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>24</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Blog Section</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">blog</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"120 characters\">{&quot;blog_title&quot;:&quot;Our [[color_text=Blogs]]&quot;,&quot;blog_subtitle&quot;:&quot;It is long established fact that a reader will be Distracted&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-03-20 08:07:55</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 11:52:04</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2036</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n            <span class=sf-dump-index>12</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\LandingPage\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LandingPage</span></span> {<a class=sf-dump-ref>#2037</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">landing_pages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>28</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Newsletter Section<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">newsletter</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"372 characters\">{&quot;title&quot;:&quot;Subscribe to our Newsletter!&quot;,&quot;sub_title&quot;:&quot;By subscribing you agree to with our Privacy Policy and provide consent to receive updates from our company.&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/GAJP88S1oigb3GIGBji1.png&quot;,&quot;button_label&quot;:&quot;Join Us&quot;,&quot;subtitle&quot;:&quot;Get weekly update about our product on your email, no spam guaranteed we promise \\u270c\\ufe0f&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2022-10-22 13:54:48</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 16:59:22</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>28</span>\n                \"<span class=sf-dump-key>theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Newsletter Section<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">newsletter</span>\"\n                \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"372 characters\">{&quot;title&quot;:&quot;Subscribe to our Newsletter!&quot;,&quot;sub_title&quot;:&quot;By subscribing you agree to with our Privacy Policy and provide consent to receive updates from our company.&quot;,&quot;image&quot;:&quot;global\\/uploads\\/landing-contents\\/image\\/GAJP88S1oigb3GIGBji1.png&quot;,&quot;button_label&quot;:&quot;Join Us&quot;,&quot;subtitle&quot;:&quot;Get weekly update about our product on your email, no spam guaranteed we promise \\u270c\\ufe0f&quot;}</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>sort</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2022-10-22 13:54:48</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 16:59:22</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>content</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2035</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n      </samp>]\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">app/Http/Controllers/Frontend/HomeController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">frontend::home.index</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>homeContent</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22050 title=\"2 occurrences\">#2050</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>46</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"44 characters\">App\\Http\\Controllers\\Frontend\\HomeController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>265</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"53 characters\">[object App\\Http\\Controllers\\Frontend\\HomeController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">home</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>211</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"37 characters\">app/Http/Middleware/IsMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Http\\Middleware\\IsMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"27 characters\">app/Http/Middleware/XSS.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>25</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Http\\Middleware\\XSS</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">modules/installer/src/Http/Middleware/ValidateLicense.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Remotelywork\\Installer\\Http\\Middleware\\ValidateLicense</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"54 characters\">modules/installer/src/Http/Middleware/InstallCheck.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Remotelywork\\Installer\\Http\\Middleware\\InstallCheck</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"36 characters\">app/Http/Middleware/Localization.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Middleware\\Localization</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"9 characters\">index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        }\n", "\n", "        if (! isset($this->hints[$segments[0]])) {\n", "            throw new InvalidArgumentException(\"No hint path defined for [{$segments[0]}].\");\n", "        }\n", "\n", "        return $segments;\n"], "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FFileViewFinder.php:111", "ajax": false, "filename": "FileViewFinder.php", "line": "111"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.820167, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php:1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.833115, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php:1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.842367, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php:1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.851022, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php:1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.861176, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php:1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.869106, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php:1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.877607, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php:1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.885122, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php:1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.893836, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.90404, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php:1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.914827, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php:1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.366294, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php:1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.374894, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php:1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.382838, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php:1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.390498, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php:1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.397426, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php:1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.421816, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php:1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.465824, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php:1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.473223, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php:1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.48096, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php:1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.489113, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php:1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.496287, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php:1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.020239999999999998, "accumulated_duration_str": "20.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'NqrBUu0fQa65JjicUwEYqH1ImIvOdWIb5ExTRXs1' limit 1", "type": "query", "params": [], "bindings": ["NqrBUu0fQa65JjicUwEYqH1ImIvOdWIb5ExTRXs1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.6142201, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 6.67}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.627686, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 6.67, "width_percent": 19.664}, {"sql": "select * from `themes` where `type` = 'landing' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["landing", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 18}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.641763, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:18", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 18}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php:18", "ajax": false, "filename": "HomeController.php", "line": "18"}, "connection": "orexcoin", "explain": null, "start_percent": 26.334, "width_percent": 2.421}, {"sql": "select `name` from `themes` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 268}, {"index": 31, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 26}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.651933, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "helpers.php:268", "source": {"index": 17, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 268}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:268", "ajax": false, "filename": "helpers.php", "line": "268"}, "connection": "orexcoin", "explain": null, "start_percent": 28.755, "width_percent": 5.188}, {"sql": "select * from `landing_pages` where `theme` = 'default' and `locale` = 'en' and not `code` = 'footer' and `status` = 1 order by `sort` asc", "type": "query", "params": [], "bindings": ["default", "en", "footer", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.654188, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:30", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 30}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php:30", "ajax": false, "filename": "HomeController.php", "line": "30"}, "connection": "orexcoin", "explain": null, "start_percent": 33.943, "width_percent": 25.099}, {"sql": "select * from `landing_contents` where `landing_contents`.`type` in ('blog', 'earning-calculation', 'faqs', 'feature-of-mining', 'hero', 'how-it-operate', 'mining-solutions', 'newsletter', 'payment-record', 'scheme', 'supported-currencies', 'testimonial', 'why-work-with-us') and `locale` = 'en'", "type": "query", "params": [], "bindings": ["blog", "earning-calculation", "faqs", "feature-of-mining", "hero", "how-it-operate", "mining-solutions", "newsletter", "payment-record", "scheme", "supported-currencies", "testimonial", "why-work-with-us", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 30}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.665912, "duration": 0.00533, "duration_str": "5.33ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:30", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 30}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php:30", "ajax": false, "filename": "HomeController.php", "line": "30"}, "connection": "orexcoin", "explain": null, "start_percent": 59.042, "width_percent": 26.334}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.672926, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 85.375, "width_percent": 3.261}, {"sql": "select * from `landing_pages` where `theme` = 'default' and `locale` = 'en' and `code` not in ('footer', 'hero', 'how-it-operate', 'scheme', 'earning-calculation', 'payment-record', 'mining-solutions', 'feature-of-mining', 'supported-currencies', 'why-work-with-us', 'faqs', 'testimonial', 'blog', 'newsletter') and `status` = 1 order by `sort` asc", "type": "query", "params": [], "bindings": ["default", "en", "footer", "hero", "how-it-operate", "scheme", "earning-calculation", "payment-record", "mining-solutions", "feature-of-mining", "supported-currencies", "why-work-with-us", "faqs", "testimonial", "blog", "newsletter", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.6745322, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:35", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 35}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php:35", "ajax": false, "filename": "HomeController.php", "line": "35"}, "connection": "orexcoin", "explain": null, "start_percent": 88.636, "width_percent": 4.101}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 250}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php", "line": 542}], "start": **********.6880438, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "orexcoin", "explain": null, "start_percent": 92.737, "width_percent": 5.534}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 64}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.818781, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:64", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 64}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FProviders%2FViewServiceProvider.php:64", "ajax": false, "filename": "ViewServiceProvider.php", "line": "64"}, "connection": "orexcoin", "explain": null, "start_percent": 98.271, "width_percent": 1.729}]}, "models": {"data": {"App\\Models\\LandingContent": {"value": 15, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLandingContent.php:1", "ajax": false, "filename": "LandingContent.php", "line": "?"}}, "App\\Models\\LandingPage": {"value": 13, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLandingPage.php:1", "ajax": false, "filename": "LandingPage.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Theme": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FTheme.php:1", "ajax": false, "filename": "Theme.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 32, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://orexcoin.test", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@home", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@home<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php:16\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php:16\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:16-43</a>", "middleware": "web", "duration": "16.8s", "peak_memory": "36MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2031777203 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2031777203\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-595580858 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-595580858\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1951821420 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"734 characters\">acceptCookies=true; XSRF-TOKEN=eyJpdiI6IkxzMWkzai92Q3Z5TFRiazlwbThqYVE9PSIsInZhbHVlIjoiQ1pRaWtvcmxhb0N4RTFJR3BiRkNHdmZuUHVZNFd5SEYwL1N4UGMzeFVOR0NnVm5XVGVRcElnWGw1TVRBZTJGWU5HNkkza1VPdTNyYTRMNjRwUVhPdDNvYkVVQ2lPYXY5ZTJRN3Rna3lqRHovWlcxRytobG5HeHYwa3V1UkxtQVMiLCJtYWMiOiJiNjNmZWJjYWQ3N2UxYjUyNDE0ZTI1ODk4NjhlMTA3Yzc3ODg3ZDdjNDgyNDlhNGJkMjI4Y2E5ODM4MDFkZTFiIiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6InptakUwWjIvZlp6a3hzc0hQYlI3QkE9PSIsInZhbHVlIjoiV1VPTTFqZHBzWTB3YVYwcnl3eXZ3S01aS2JhU0hrTnI3dGo0bFkzUDByOTNUaW01TkRlVVR6TTZyUzRwM1BkT3FGMGcwNHhWTWlUY1hHdFdvSXJZZFFId21hNk0wbkxoazFRRng2emI4VGJNZFRkNHBwRjhoTXVLamlsMHg0ZUMiLCJtYWMiOiI1ZWM2NWEzNTJlNGIwZjk4NTNmNjIwN2FlZWZlZWFkNTE3MmY3NjdiYzVjNTMzYWM0ODY1MmNkYzM2YzAyMDE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951821420\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1029724242 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>acceptCookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vSKNOOwveFCiscf7za5sjtSztXsbm77YHwNmHw3C</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NqrBUu0fQa65JjicUwEYqH1ImIvOdWIb5ExTRXs1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029724242\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-184035271 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 08:32:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184035271\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2135494620 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vSKNOOwveFCiscf7za5sjtSztXsbm77YHwNmHw3C</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"36 characters\">https://orexcoin.test/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://orexcoin.test/user/setting/kyc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRXBXNQVNBPDA</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JWTEASM9B35XPWBR4BNXV6F2</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135494620\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://orexcoin.test", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@home"}, "badge": "500 Internal Server Error"}}