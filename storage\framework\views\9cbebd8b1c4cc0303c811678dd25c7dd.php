<?php $__env->startSection('title'); ?>
    <?php echo e(__('Reply Ticket')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('css'); ?>
    <style>
        .attachment {
            width: 20px
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-xxl-12">
        <!-- Page title wrapper -->
        <div class="page-title-wrapper mb-16">
            <div class="page-title-contents">
                <h3 class="page-title"><?php echo e(__('Reply Ticket')); ?></h3>
            </div>
        </div>

        <?php echo $__env->make('frontend::user.ticket.include.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>
    <div class="col-xxl-12">
        <div class="support-ticket-chat-area">
            <div class="support-ticket-top mb-30">
                <div class="support-ticket-top-left">
                   <h6 class="title"><?php echo e(__('Support Ticket')); ?> #<?php echo e($ticket->uuid); ?></h6>
                </div>
                <div class="support-ticket-top-right">
                    <?php if($ticket->isOpen()): ?>
                    <form action="<?php echo e(route('user.ticket.close.now', $ticket->uuid)); ?>">            
                        <button type="submit" class="td-btn btn-h-40 btn-chip grd-fill-btn-primary">
                             <div class="inner-btn">
                                <span class="btn-text"><?php echo e(__('Close Ticket')); ?></span>
                             </div>
                          </button>
                    </form>
                    <?php endif; ?>
                    <?php if($ticket->isClosed()): ?>
                    <form action="<?php echo e(route('user.ticket.reopen', $ticket->uuid)); ?>">
                        <button type="submit" class="td-btn  btn-h-40 btn-chip grd-fill-btn-primary ">
                            <div class="inner-btn">
                                <span class="btn-text"><?php echo e(__('Reopen Ticket')); ?></span>
                            </div>
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
             </div>
            <div class="support-box-wrapper">
                <div class="support-chat-text-item <?php if($ticket->model == 'admin'): ?> admin-message <?php else: ?> user-message <?php endif; ?>">
                    <div class="chat-text-avatar">
                        <div class="thumb">
                            <?php if($ticket->model == 'admin'): ?>
                                <img src="<?php echo e(asset('frontend/images/users/admin.png')); ?>" alt="Admin">
                            <?php else: ?>
                                <span><?php echo e(substr($ticket->user->name ?? 'User', 0, 1)); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="message-list-box">
                        <div class="message-list">
                            <p class="description"><?php echo nl2br(e($ticket->message)); ?></p>
                            <?php if(!empty($ticket->attachments)): ?>
                                <div class="chat-attachments mt-2">
                                    <?php $__currentLoopData = ($ticket->attachments); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="attachment">
                                            <span class="screenshot">
                                                <svg width="15" height="20" viewBox="0 0 15 20" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M2.5 1.5C2.10218 1.5 1.72064 1.65804 1.43934 1.93934C1.15804 2.22064 1 2.60218 1 3V17C1 17.3978 1.15804 17.7794 1.43934 18.0607C1.72064 18.342 2.10218 18.5 2.5 18.5H12.5C12.8978 18.5 13.2794 18.342 13.5607 18.0607C13.842 17.7794 14 17.3978 14 17V7.41411C13.9999 7.28155 13.9473 7.15433 13.8535 7.06061L8.43945 1.64655C8.34573 1.5528 8.21856 1.50006 8.086 1.5H2.5ZM0.732233 1.23223C1.20107 0.763392 1.83696 0.5 2.5 0.5H8.086C8.48377 0.500085 8.86532 0.658156 9.14655 0.939447M9.14655 0.939447L14.5605 6.35339C14.8418 6.63463 14.9999 7.01613 15 7.41389V17C15 17.663 14.7366 18.2989 14.2678 18.7678C13.7989 19.2366 13.163 19.5 12.5 19.5H2.5C1.83696 19.5 1.20107 19.2366 0.732233 18.7678C0.263392 18.2989 0 17.663 0 17V3C0 2.33696 0.263392 1.70107 0.732233 1.23223"
                                                        fill="#52A2FF"></path>
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M4.4395 10.9392L1 14.3787V15.1716C1 15.4477 0.776142 15.6716 0.5 15.6716C0.223858 15.6716 0 15.4477 0 15.1716V14.1716C0 14.039 0.0526784 13.9118 0.146447 13.818L3.73245 10.232C4.20127 9.76334 4.83709 9.5 5.5 9.5C6.16291 9.5 6.79868 9.76329 7.2675 10.232L9.5 12.4645L10.7324 11.232C11.2013 10.7633 11.8371 10.5 12.5 10.5C13.1629 10.5 13.7987 10.7633 14.2675 11.232L14.8536 11.818C14.9473 11.9118 15 12.039 15 12.1716V13.1716C15 13.4477 14.7761 13.6716 14.5 13.6716C14.2239 13.6716 14 13.4477 14 13.1716V12.3787L13.5605 11.9392C13.2792 11.658 12.8977 11.5 12.5 11.5C12.1023 11.5 11.7208 11.658 11.4396 11.9391L10.2071 13.1716L11.8536 14.818C12.0488 15.0133 12.0488 15.3299 11.8536 15.5251C11.6583 15.7204 11.3417 15.7204 11.1464 15.5251L6.5605 10.9392C6.27921 10.658 5.89772 10.5 5.5 10.5C5.10228 10.5 4.72079 10.658 4.4395 10.9392Z"
                                                        fill="#52A2FF"></path>
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M9 8C9 7.72386 9.22386 7.5 9.5 7.5H9.51C9.78614 7.5 10.01 7.72386 10.01 8C10.01 8.27614 9.78614 8.5 9.51 8.5H9.5C9.22386 8.5 9 8.27614 9 8Z"
                                                        fill="#52A2FF"></path>
                                                </svg>
                                            </span>
                                            <span class="text"><?php echo e(getBasename($attachment)); ?></span>
                                            <a href="<?php echo e(asset($attachment)); ?>" target="_blank" class="btn btn-sm btn-link">
                                                <iconify-icon icon="tabler:download"></iconify-icon>
                                            </a>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="support-meta d-flex justify-content-between flex-wrap gap-2">
                            <span class="author">
                                <?php echo e($ticket->user->name ?? 'User'); ?>

                            </span>
                            <span class="timestamp"><?php echo e($ticket->created_at->format('H:i')); ?></span>
                        </div>



                    </div>
                </div>

                <?php $__currentLoopData = $ticket->messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="support-chat-text-item <?php if($message->model == 'admin'): ?> admin-message <?php else: ?> user-message <?php endif; ?>">
                        <div class="chat-text-avatar">
                            <div class="thumb">
                                <?php if($message->model == 'admin'): ?>
                                    <img src="<?php echo e(asset($message->user->avatar_path)); ?>" alt="Admin">
                                <?php else: ?>
                                    <span><?php echo e(substr($ticket->user->name ?? 'User', 0, 1)); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="message-list-box">
                            <div class="message-list">
                                <p class="description"><?php echo nl2br(e($message->message)); ?></p>
                                <?php if(!empty($message->attach)): ?>
                                    <div class="chat-attachments mt-2">
                                        <?php $__currentLoopData = ($message->attach); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="attachment">
                                                <span class="screenshot">
                                                    <svg width="15" height="20" viewBox="0 0 15 20" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                            d="M2.5 1.5C2.10218 1.5 1.72064 1.65804 1.43934 1.93934C1.15804 2.22064 1 2.60218 1 3V17C1 17.3978 1.15804 17.7794 1.43934 18.0607C1.72064 18.342 2.10218 18.5 2.5 18.5H12.5C12.8978 18.5 13.2794 18.342 13.5607 18.0607C13.842 17.7794 14 17.3978 14 17V7.41411C13.9999 7.28155 13.9473 7.15433 13.8535 7.06061L8.43945 1.64655C8.34573 1.5528 8.21856 1.50006 8.086 1.5H2.5ZM0.732233 1.23223C1.20107 0.763392 1.83696 0.5 2.5 0.5H8.086C8.48377 0.500085 8.86532 0.658156 9.14655 0.939447M9.14655 0.939447L14.5605 6.35339C14.8418 6.63463 14.9999 7.01613 15 7.41389V17C15 17.663 14.7366 18.2989 14.2678 18.7678C13.7989 19.2366 13.163 19.5 12.5 19.5H2.5C1.83696 19.5 1.20107 19.2366 0.732233 18.7678C0.263392 18.2989 0 17.663 0 17V3C0 2.33696 0.263392 1.70107 0.732233 1.23223"
                                                            fill="#52A2FF"></path>
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                            d="M4.4395 10.9392L1 14.3787V15.1716C1 15.4477 0.776142 15.6716 0.5 15.6716C0.223858 15.6716 0 15.4477 0 15.1716V14.1716C0 14.039 0.0526784 13.9118 0.146447 13.818L3.73245 10.232C4.20127 9.76334 4.83709 9.5 5.5 9.5C6.16291 9.5 6.79868 9.76329 7.2675 10.232L9.5 12.4645L10.7324 11.232C11.2013 10.7633 11.8371 10.5 12.5 10.5C13.1629 10.5 13.7987 10.7633 14.2675 11.232L14.8536 11.818C14.9473 11.9118 15 12.039 15 12.1716V13.1716C15 13.4477 14.7761 13.6716 14.5 13.6716C14.2239 13.6716 14 13.4477 14 13.1716V12.3787L13.5605 11.9392C13.2792 11.658 12.8977 11.5 12.5 11.5C12.1023 11.5 11.7208 11.658 11.4396 11.9391L10.2071 13.1716L11.8536 14.818C12.0488 15.0133 12.0488 15.3299 11.8536 15.5251C11.6583 15.7204 11.3417 15.7204 11.1464 15.5251L6.5605 10.9392C6.27921 10.658 5.89772 10.5 5.5 10.5C5.10228 10.5 4.72079 10.658 4.4395 10.9392Z"
                                                            fill="#52A2FF"></path>
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                            d="M9 8C9 7.72386 9.22386 7.5 9.5 7.5H9.51C9.78614 7.5 10.01 7.72386 10.01 8C10.01 8.27614 9.78614 8.5 9.51 8.5H9.5C9.22386 8.5 9 8.27614 9 8Z"
                                                            fill="#52A2FF"></path>
                                                    </svg>
                                                </span>
                                                <span class="text"><?php echo e(getBasename($attachment)); ?></span>
                                                <a href="<?php echo e(asset($attachment)); ?>" target="_blank" class="btn btn-sm btn-link">
                                                    <iconify-icon icon="tabler:download"></iconify-icon>
                                                </a>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php endif; ?>

                            </div>
                            <div class="support-meta d-flex justify-content-between flex-wrap gap-2">
                                <span class="author">
                                    <?php echo e($message->model == 'admin' ? 'Admin' : ($ticket->user->name ?? 'User')); ?>

                                </span>
                                <span class="timestamp"><?php echo e($message->created_at->format('H:i')); ?></span>
                            </div>



                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <div class="support-attachment-form">
                    <form id="replyForm" action="<?php echo e(route('user.ticket.reply')); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="uuid" value="<?php echo e($ticket->uuid); ?>">

                        <div class="upload-chat-attachments" id="attachmentPreview">

                        </div>

                        <textarea name="message" id="replyMessage" placeholder="Write your reply here" required></textarea>

                        <div class="support-input-box">
                            <div class="attachment-actions-buttons">
                                <button type="button" id="addAttachmentBtn" class="td-btn btn-chip grd-outline-fill-btn primary-btn btn-m-w">
                                    <span class="inner-btn">
                                        <span class="btn-text"><?php echo e(__('Add attachment')); ?></span>
                                    </span>
                                </button>
                                <button type="submit" class="td-btn btn-chip grd-fill-btn-primary btn-m-w">
                                    <span class="btn-icon">
                                        <svg width="16" height="14" viewBox="0 0 16 14" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <path d="M7.07513 7.0005H3.17881M3.11884 7.20701L1.46472 12.1481C1.33477 12.5363 1.2698 12.7303 1.31643 12.8499C1.35692 12.9537 1.44389 13.0324 1.5512 13.0623C1.67478 13.0968 1.86142 13.0128 2.23472 12.8448L14.0735 7.51737C14.4379 7.35337 14.6201 7.2714 14.6764 7.15749C14.7254 7.05859 14.7254 6.94248 14.6764 6.84351C14.6201 6.72967 14.4379 6.64763 14.0735 6.4837L2.23059 1.1544C1.85842 0.98692 1.67234 0.903184 1.54889 0.937535C1.44168 0.967367 1.35471 1.04585 1.31408 1.14945C1.26729 1.26875 1.33157 1.46242 1.46013 1.84975L3.1193 6.84861C3.14138 6.91513 3.15242 6.94836 3.15678 6.98236C3.16065 7.01261 3.16061 7.04315 3.15666 7.07333C3.15222 7.10733 3.14109 7.14055 3.11884 7.20701Z"
                                                    stroke="white" stroke-width="1.2" stroke-linecap="round"
                                                    stroke-linejoin="round"></path>
                                        </svg>
                                    </span>
                                    <span class="btn-text"><?php echo e(__('Send')); ?></span>
                                </button>
                            </div>
                        </div>

                        <input type="file" class="d-none" name="attach[]" id="attachmentInput" multiple accept=".gif,.jpg,.png,.jpeg,.webp,.pdf,.svg">
                    </form>
                </div>

            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
$(document).ready(function () {
    const fileInput = $('#attachmentInput');
    const previewContainer = $('#attachmentPreview');
    const addBtn = $('#addAttachmentBtn');

    addBtn.on('click', function () {
        fileInput.click();
    });

    fileInput.on('change', function () {
        const files = this.files;

        $.each(files, function (index, file) {
            const reader = new FileReader();

            reader.onload = function (e) {
                let iconHTML = `<img src="${e.target.result}" alt="Attachment Screenshot">`;

                if (!file.type.startsWith('image/')) {
                    iconHTML = `<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M25 10V25C25 26.6569 23.6569 28 22 28H8C6.34315 28 5 26.6569 5 25V5C5 3.34315 6.34315 2 8 2H17.5L25 10Z" stroke="#999" stroke-width="2"/>
                                    <text x="10" y="20" font-size="10" fill="#999">.${getFileExtension(file.name)}</text>
                                </svg>`;
                }

                const attachmentHTML = `
                    <div class="attachment" data-filename="${file.name}">
                        <span class="screenshot">${iconHTML}</span>
                        <span class="text">${file.name}</span>
                        <button class="close remove-attachment">
                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.37937 2.62077C9.58858 2.82998 9.58858 3.16918 9.37937 3.37839L3.37937 9.37839C3.17016 9.5876 2.83096 9.5876 2.62175 9.37839C2.41254 9.16918 2.41254 8.82998 2.62175 8.62077L8.62175 2.62077C8.83096 2.41156 9.17016 2.41156 9.37937 2.62077Z" fill="white"/>
                                <path d="M2.62175 2.62077C2.83096 2.41156 3.17016 2.41156 3.37937 2.62077L9.37937 8.62077C9.58858 8.82998 9.58858 9.16918 9.37937 9.37839C9.17016 9.5876 8.83096 9.5876 8.62175 9.37839L2.62175 3.37839C2.41254 3.16918 2.41254 2.82998 2.62175 2.62077Z" fill="white"/>
                            </svg>
                        </button>
                    </div>
                `;

                previewContainer.append(attachmentHTML);
            };

            reader.readAsDataURL(file);
        });
    });

    $(document).on('click', '.remove-attachment', function () {
        $(this).closest('.attachment').remove();
    });


    function getFileExtension(filename) {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
    }
});
    
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/ticket/show.blade.php ENDPATH**/ ?>