<?php $__env->startSection('title'); ?>
    <?php echo e(__('Submission')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('frontend::user.settings.include.__setting_menu_list', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<div class="col-xxl-12">
    <div class="add-money-area default-area-style">
        <div class="heading-top">
            <h5 class="title"><?php echo e($kyc->name); ?></h5>
        </div>
        <div class="default-content-inner">
            <div class="profile-setting-form">
                <form action="<?php echo e(route('user.kyc.submit')); ?>" method="post" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="kyc_id" value="<?php echo e(encrypt($kyc->id)); ?>">
    
                    <div class="row gy-24">
                        <?php $__currentLoopData = json_decode($kyc->fields, true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($field['type'] == 'file'): ?>
                                <div class="col-lg-12">
                                    <div class="td-form-group">
                                        <label class="input-label" for="image-<?php echo e($key); ?>">
                                            <?php echo e($field['name']); ?>

                                            <?php if($field['validation'] == 'required'): ?><span>*</span><?php endif; ?>
                                        </label>
                                        <div class="input-field">
                                            <div id="uploadItems">
                                                <div class="upload-custom-file">
                                                    <input type="file"
                                                           name="kyc_credential[<?php echo e($field['name']); ?>]"
                                                           class="upload-input"
                                                           accept=".gif, .jpg, .png, .jpeg, .webp, .pdf"
                                                           id="image-<?php echo e($key); ?>">
                                                    <label for="image-<?php echo e($key); ?>">
                                                        <img class="upload-icon" src="<?php echo e(frontendAsset('images/icons/upload.svg')); ?>" alt="upload file">
                                                        <span><b><?php echo e(__('Attach File')); ?></b></span>
                                                    </label>
                                                    <button type="button" class="file-upload-close" style="display: none;">
                                                        <i><iconify-icon icon="tabler:circle-x"></iconify-icon></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <?php $__errorArgs = ["kyc_credential.{$field['name']}"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="feedback-invalid"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
    
                            <?php elseif($field['type'] == 'textarea'): ?>
                                <div class="col-lg-12">
                                    <div class="td-form-group">
                                        <label class="input-label" for="kyc_textarea_<?php echo e($key); ?>">
                                            <?php echo e($field['name']); ?>

                                            <?php if($field['validation'] == 'required'): ?><span>*</span><?php endif; ?>
                                        </label>
                                        <div class="input-field">
                                            <textarea class="form-control"
                                                      name="kyc_credential[<?php echo e($field['name']); ?>]"
                                                      id="kyc_textarea_<?php echo e($key); ?>"></textarea>
                                        </div>
                                        <?php $__errorArgs = ["kyc_credential.{$field['name']}"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="feedback-invalid"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
    
                            <?php else: ?>
                                <div class="col-lg-12">
                                    <div class="td-form-group">
                                        <label class="input-label" for="kyc_input_<?php echo e($key); ?>">
                                            <?php echo e($field['name']); ?>

                                            <?php if($field['validation'] == 'required'): ?><span>*</span><?php endif; ?>
                                        </label>
                                        <div class="input-field">
                                            <input type="text"
                                                   class="form-control"
                                                   name="kyc_credential[<?php echo e($field['name']); ?>]"
                                                   id="kyc_input_<?php echo e($key); ?>"
                                                   <?php if($field['validation'] == 'required'): ?> required <?php endif; ?>>
                                        </div>
                                        <?php $__errorArgs = ["kyc_credential.{$field['name']}"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="feedback-invalid"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    
                        <div class="col-lg-12">
                            <div class="td-form-btns d-flex justify-content-end mt-30">
                                <button type="submit" class="td-btn btn-chip grd-fill-btn-primary">
                                    <?php echo e(__('Submit Now')); ?>

                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        "use strict";
        $(document).on('change', 'input[type="file"]', function(event) {
            var $file = $(this),
                $label = $file.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            var fileName = $file.val().split('\\').pop(),
                file = event.target.files[0],
                fileType = file ? file.type.split('/')[0] : null,
                tmppath = file ? URL.createObjectURL(file) : null;

            if (fileName) {
                if (fileType === "image") {

                    $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
                } else {

                    $label.addClass('file-ok').css('background-image', 'none');
                }
                $labelText.text(fileName);
                $typeFileText.hide();
                $label.siblings('.file-upload-close').show();
            } else {
                resetUpload($file, $label, $labelText, $typeFileText,
                    labelDefault);
            }
        });

        $(document).on('click', '.file-upload-close', function() {
            var $button = $(this),
                $uploadWrapper = $button.closest('.upload-custom-file'),
                $fileInput = $uploadWrapper.find('input[type="file"]'),
                $label = $fileInput.next('label'),
                $labelText = $label.find('span:first'),
                $typeFileText = $label.find('.type-file-text'),
                labelDefault = "Upload Image";

            resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
        });

        function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
            $fileInput.val('');
            $label.removeClass('file-ok').css('background-image', 'none');
            $labelText.text(labelDefault);
            $typeFileText.show();
            $label.siblings('.file-upload-close').hide();
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/kyc/submission.blade.php ENDPATH**/ ?>