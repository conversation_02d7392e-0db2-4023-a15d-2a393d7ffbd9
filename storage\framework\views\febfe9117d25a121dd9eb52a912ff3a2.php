<?php use \App\Models\Coin; ?>
<?php use \App\Models\Scheme; ?>
<?php
   $scheme = Scheme::with('miner.coin')->where('status', true)->get();
   $coins = Coin::active()->get();
?>
<div class="pricing-tab-wrapper has_fade_anim  mb-30">
    <div class="pricing-tab-filter td-tab <?php echo e(Route::is('user.mining.*') ? '' : 'style-two'); ?>">
      <div class="inner">
        <nav>
           <div class="nav nav-tabs" id="nav-tab" role="tablist">
             <?php $__currentLoopData = $coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <button class="nav-link <?php echo e($loop->index == 0 ? ' active' : ''); ?>" id="nav-mining-<?php echo e(str($coin->code)->slug('-')); ?>-tab" data-bs-toggle="tab"
              data-bs-target="#nav-mining-<?php echo e(str($coin->code)->slug('-')); ?>" type="button" role="tab" aria-controls="nav-mining-<?php echo e(str($coin->code)->slug('-')); ?>"
              
              aria-selected="true">
              <div class="inner">
               <span class="btn-icon"><img src="<?php echo e(asset($coin->icon)); ?>" alt=""></span>
               <span class="btn-text"><?php echo e($coin->name); ?></span>
              </div>
            </button>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
           </div>
        </nav>
      </div>
    </div>
  </div>
  <div class="pricing-tab-contents">
    <div class="tab-content" id="nav-tabContent">
      <?php $__currentLoopData = $scheme->groupBy(['miner.coin_id', 'miner.coin.code']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coinId => $schemes): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
           <?php
           $schemes = $schemes->first();
           $coinSymbol = $schemes->first()->miner->coin->code;              
        ?>
       <div class="tab-pane fade <?php echo e($loop->index == 0 ? 'show active' : ''); ?>" id="nav-mining-<?php echo e(str($schemes->first()->miner->coin->code)->slug('-')); ?>" role="tabpanel"
        aria-labelledby="nav-mining-<?php echo e(str($schemes->first()->miner->coin->code)->slug('-')); ?>-tab" tabindex="0">
        <div class="row gy-50">
           <?php $__currentLoopData = $schemes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $scheme): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
           <div class="col-lg-<?php echo e(Route::is('home') ? 4 : 3); ?> col-md-6">
                <div class="pricing-item <?php echo e(Route::is('user.mining.*') ? '' : 'style-two'); ?> has_fade_anim" data-delay="<?php echo e(0.15 * $key+1); ?>">
                    <div class="pricing-card">
                        <div class="pricing-clip-path">
                            <div class="pricing-card-inner">
                                <div class="plan-contents">
                                    <h2 class="plan-title"><?php echo e($scheme->name); ?></h2>
                                    <p class="plan-subtitle"><?php echo e($scheme->miner->name); ?></p>
                                    <div class="plan-price">
                                        <?php echo e(formatAmount($scheme->price, $currencySymbol, true)); ?>

                                    </div>
                                    <div class="features-list">
                                        <ul>
                                         <ul class="feature-list">
                                            <li>
                                                <span class="feature-check">
                                                    <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                </span>
                                                <span class="feature-text">
                                                    <?php echo e(__('Return Amount')); ?>:
                                                    <?php echo e($scheme->return_amount_type == 'fixed' ?
                                                       formatAmount($scheme->return_amount_value, $scheme->miner->coin->code, true) :
                                                       formatAmount($scheme->return_min_amount, $scheme->miner->coin->code, true) . ' - ' .
                                                       formatAmount($scheme->return_max_amount, $scheme->miner->coin->code, true)); ?>

                                                       / <?php echo e(__('per')); ?> <?php echo e($scheme->return_period_hours); ?> <?php echo e(__('Hours')); ?>

                                                </span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                </span>
                                                <span class="feature-text"><?php echo e(__('Uptime')); ?>: <?php echo e($scheme->miner->uptime); ?>%</span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                </span>
                                                <span class="feature-text"><?php echo e(__('Renewable Energy')); ?>: <?php echo e($scheme->miner->renewable_energy); ?>%</span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                </span>
                                                <span class="feature-text"><?php echo e(__('Return Period Type')); ?>: <?php echo e($scheme->schedule->name); ?></span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                </span>
                                                <span class="feature-text"><?php echo e(__('Return Period')); ?>: <?php echo e($scheme->return_period_type == 'period' ? ($scheme->return_period_max_number . ' ' . __('times') ): __('Lifetime')); ?></span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                </span>
                                                <span class="feature-text"><?php echo e(__('Network Hashrate')); ?>: <?php echo e($scheme->miner->network_hashrate_amount); ?> <?php echo e($scheme->miner->network_hashrate); ?></span>
                                            </li>
                                            <li>
                                                <span class="feature-check">
                                                    <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                </span>
                                                <span class="feature-text">Speed: <?php echo e($scheme->speed_amount); ?> <?php echo e($scheme->speed); ?></span>
                                            </li>                                      
                                            <li>
                                                <span class="feature-check">
                                                    <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                </span>
                                                <span class="feature-text">
                                                    <?php echo e($scheme->maintenance_fee_type == 'percentage' ? $scheme->maintenance_fee_amount . '%' : formatAmount($scheme->maintenance_fee_amount, $scheme->miner->coin->code, true)); ?>

                                                    <?php echo e(__('Maintenance Fee')); ?>

                                                </span>
                                            </li>
                                        
                                            <?php if($scheme->max_mining_amount > 0): ?>
                                                <li>
                                                    <span class="feature-check">
                                                        <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                    </span>
                                                    <span class="feature-text">
                                                        <?php echo e(formatAmount($scheme->max_mining_amount, $scheme->miner->coin->code, true)); ?> <?php echo e(__('Max Mining Amount')); ?>

                                                    </span>
                                                </li>
                                            <?php endif; ?>
                                        
                                            <?php if($scheme->is_featured): ?>
                                                <li>
                                                    <span class="feature-check">
                                                        <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                    </span>
                                                    <span class="feature-text"><?php echo e(__('Featured Plan')); ?></span>
                                                </li>
                                            <?php endif; ?>
                                        
                                            <?php if(!empty($scheme->holidays)): ?>
                                                <li>
                                                    <span class="feature-check">
                                                        <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                    </span>
                                                    <span class="feature-text"><?php echo e(__('Holidays: ') . implode(', ', $scheme->holidays)); ?></span>
                                                </li>
                                            <?php endif; ?>              
                                            <?php if(!empty($scheme->features) && is_array($scheme->features)): ?>
                                                <?php $__currentLoopData = $scheme->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li>
                                                        <span class="feature-check">
                                                            <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                        </span>
                                                        <span class="feature-text"><?php echo e($feature); ?></span>
                                                    </li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                                <li>
                                                    <span class="feature-check">
                                                        <img src="<?php echo e(frontendAsset('images/icons/check.svg')); ?>" alt="check icon">
                                                    </span>
                                                    <span class="feature-text"><?php echo e(__('Standard mining operations')); ?></span>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                </div>
                                <div class="plan-link">
                                    <a href="<?php echo e(route('user.plan-purchase.checkout',$scheme->id)); ?>" class="td-btn btn-chip grd-fill-btn-secondary w-100"><?php echo e(__('Get Started')); ?></a>
                                </div>
                            </div>
                        </div>
                        <div class="plan-icon" data-background="<?php echo e(frontendAsset('images/icons/gradient-border.png')); ?>" style="background-image: url('<?php echo e(frontendAsset('images/icons/gradient-border.png')); ?>');">
                            <img src="<?php echo e(asset($scheme->icon)); ?>" alt="<?php echo e($scheme->name); ?>">
                        </div>
                        <div class="bg-shape" data-background="<?php echo e(frontendAsset('images/pricing/price-dot-bg.png')); ?>" style="background-image: url('<?php echo e(frontendAsset('images/pricing/price-dot-bg.png')); ?>');"></div>
                    </div>
                </div>
          </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
       </div>
     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <div class="pricing-overlay-bg" data-background="<?php echo e(frontendAsset('images/bg/pricing-overlay-bg.png')); ?>"></div>
    <div class="pricing-dot-bg" data-background="<?php echo e(frontendAsset('images/bg/pricing-dot.png')); ?>"></div>

  </div><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/mining/include/pricing.blade.php ENDPATH**/ ?>