<?php $__env->startSection('title'); ?>
    <?php echo e(__($notify['type']->value.' Success')); ?>

<?php $__env->stopSection(); ?>
<?php use \App\Enums\TxnType; ?>

<?php $__env->startSection('content'); ?>
<div class="col-xxl-12">

    <!-- Page title wrapper -->
    <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
            <h3 class="page-title"><?php echo e(__($notify['type']->value)); ?></h3>
        </div>
    </div>
    <!-- Page title wrapper -->

    <!-- Pages navigation start -->
    <?php if(in_array($notify['_type'], [TxnType::PlanPurchase->value, TxnType::PlanPurchaseManual->value])): ?>
        <?php else: ?>
        <?php echo $__env->make('frontend::user.add_money._topbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>
    <!-- Pages navigation end -->

</div>
<div class="col-xxl-12">
    <?php if (isset($component)) { $__componentOriginalb765192ec690858b58e2a45381300bff = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb765192ec690858b58e2a45381300bff = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.trnx-success','data' => ['status' => ''.e($notify['status']).'','type' => ''.e(__($notify['type']->value)).'','amount' => ''.e($notify['amount']).'','tnx' => ''.e($notify['tnxID']).'','action' => ''.e(in_array($notify['_type'], [TxnType::PlanPurchase->value, TxnType::PlanPurchaseManual->value]) ? route('user.mining.') : route('user.addMoney')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('trnx-success'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => ''.e($notify['status']).'','type' => ''.e(__($notify['type']->value)).'','amount' => ''.e($notify['amount']).'','tnx' => ''.e($notify['tnxID']).'','action' => ''.e(in_array($notify['_type'], [TxnType::PlanPurchase->value, TxnType::PlanPurchaseManual->value]) ? route('user.mining.') : route('user.addMoney')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb765192ec690858b58e2a45381300bff)): ?>
<?php $attributes = $__attributesOriginalb765192ec690858b58e2a45381300bff; ?>
<?php unset($__attributesOriginalb765192ec690858b58e2a45381300bff); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb765192ec690858b58e2a45381300bff)): ?>
<?php $component = $__componentOriginalb765192ec690858b58e2a45381300bff; ?>
<?php unset($__componentOriginalb765192ec690858b58e2a45381300bff); ?>
<?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/add_money/success.blade.php ENDPATH**/ ?>