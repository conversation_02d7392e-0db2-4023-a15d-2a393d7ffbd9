@extends('backend.layouts.app')

@section('title')
    {{ __('Create Coin') }}
@endsection

@section('content')
    <div class="main-content">
        <div class="container-fluid">
            <div class="page-title">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col">
                            <div class="title-content">
                                <h2 class="title">{{ __('Create Coin') }}</h2>
                                <div>
                                    <a href="{{ route('admin.coin.index') }}" class="title-btn">
                                        <i icon-name="list"></i>
                                        {{ __('List') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-8">
                    <div class="site-card">
                        <div class="site-card-body">
                            <form action="{{ route('admin.coin.store') }}" class="row" method="POST"
                                enctype="multipart/form-data">
                                @csrf
                                <div class="row">
                                    <div class="col-xl-3">
                                        <div class="site-input-groups">
                                            <x-admin.drag-and-drop required name="icon" label="{{ __('Coin Logo:') }}" />
                                            @error('icon')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label">
                                            {{ __('Name') }}: 
                                            <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="box-input" name="name" value="{{ old('name') }}">
                                        @error('name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-xl-2">
                                    <div class="site-input-groups">
                                        <label class="box-input-label">{{ __('Symbol') }}: 
                                            <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="box-input" name="symbol" value="{{ old('symbol') }}">
                                        @error('symbol')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-xl-4">
                                    <div class="site-input-groups">
                                        <label class="box-input-label">{{ __('Conversion Rate') }}: 
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group joint-input">
                                            <span class="input-group-text">1 {{ setting('site_currency', 'global') }} = </span>
                                            <input step="0.{{ str_repeat('0', 7) }}1" type="number" class="form-control" name="conversion_rate" value="{{ cryptoFormat(old('conversion_rate')) }}">
                                            <span class="input-group-text coin-code"></span>
                                        </div>
                                        @error('symbol')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label">
                                            {{ __('Code:') }} <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="box-input" name="code" id="code"
                                            value="{{ old('code') }}">
                                        @error('code')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label">
                                            {{ __('Status:') }} 
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="switch-field same-type">
                                            <input type="radio" id="radio-five" name="status" value="active"
                                                @if (old('status') == 'active') checked @endif checked>
                                            <label for="radio-five">{{ __('Active') }}</label>
                                            <input type="radio" id="radio-six" name="status" value="inactive"
                                                @if (old('status') == 'inactive') checked @endif>
                                            <label for="radio-six">{{ __('Inactive') }}</label>
                                        </div>
                                        @error('status')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-xl-12">
                                    <button type="submit" class="site-btn primary-btn w-100">
                                        {{ __('Submit') }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $("#code").on('change', function() {
            $('#currency-selected').text(this.value);
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to toggle visibility of the Exchange Charges section
            function toggleExchangeCharge() {
                var isChecked = document.querySelector('input[name="exchange_charge_status"]:checked').value ===
                    '1';
                document.getElementById('exchange-charges-section').style.display = isChecked ? 'block' : 'none';
            }

            // Function to toggle visibility of the Transfer Charges section
            function toggleTransferCharge() {
                var isChecked = document.querySelector('input[name="transfer_charge_status"]:checked').value ===
                    '1';
                document.getElementById('transfer-charges-section').style.display = isChecked ? 'block' : 'none';
            }

            // Attach event listeners to radio buttons
            document.querySelectorAll('input[name="exchange_charge_status"]').forEach(function(element) {
                element.addEventListener('change', toggleExchangeCharge);
            });

            document.querySelectorAll('input[name="transfer_charge_status"]').forEach(function(element) {
                element.addEventListener('change', toggleTransferCharge);
            });
            $(document).on('input', 'input[name="code"]', function() {
                $('.coin-code').text(this.value);
            });

            // Initial check on page load
            toggleExchangeCharge();
            toggleTransferCharge();
        });
    </script>
@endsection
