<!doctype html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" <?php echo when(isRtl(app()->getLocale()), 'dir="rtl"'); ?>>

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title><?php echo $__env->yieldContent('title'); ?> | <?php echo e(setting('site_title', 'global')); ?></title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" type="image/x-icon" href="<?php echo e(asset(setting('site_favicon', 'global'))); ?>">
    <!-- CSS here -->
    <?php echo $__env->make('frontend::layouts.style', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    <?php echo $__env->yieldPushContent('style'); ?>
</head>

<body class="<?php echo e(getThemeBodyClass()); ?>">
    
    <?php echo $__env->make('frontend::include._notify', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="page-wrapper null compact-wrapper">
        <?php echo $__env->make('frontend::layouts.user_header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <div class="app-page-body-wrapper">
            <?php echo $__env->make('frontend::layouts.user_sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <div class="app-page-body">
                <div class="row <?php echo e($gyClass ?? 'gy-30'); ?>">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
        </div>
    </div>
    
    <?php echo $__env->make('frontend::layouts.script', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php $__env->startPush(
    'script'
    ); ?>
        <script src="<?php echo e(frontendAsset('js/sidebar-menu.js')); ?>"></script>
        <script src="<?php echo e(frontendAsset('js/scrollbar/simplebar.js')); ?>"></script>
        <script src="<?php echo e(frontendAsset('js/scrollbar/custom.js')); ?>"></script>
    <?php $__env->stopPush(); ?>
    
    
    <?php echo $__env->yieldPushContent('js'); ?>
</body>

</html>
<?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/layouts/user.blade.php ENDPATH**/ ?>