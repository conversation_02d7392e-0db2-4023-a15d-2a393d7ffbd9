{"__meta": {"id": "01JY18RPNSK5PMZCR7BKSXQ14W", "datetime": "2025-06-18 16:18:45", "utime": **********.81914, "method": "POST", "uri": "/admin/schema", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[16:18:45] LOG.warning: strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\laragon\\www\\orexcoin\\app\\Http\\Middleware\\XSS.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.771226, "xdebug_link": null, "collector": "log"}, {"message": "[16:18:45] LOG.warning: strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\laragon\\www\\orexcoin\\app\\Http\\Middleware\\XSS.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.781083, "xdebug_link": null, "collector": "log"}, {"message": "[16:18:45] LOG.warning: strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\laragon\\www\\orexcoin\\app\\Http\\Middleware\\XSS.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.781304, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.516525, "end": **********.819163, "duration": 0.30263805389404297, "duration_str": "303ms", "measures": [{"label": "Booting", "start": **********.516525, "relative_start": 0, "end": **********.738547, "relative_end": **********.738547, "duration": 0.*****************, "duration_str": "222ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.738569, "relative_start": 0.****************, "end": **********.819166, "relative_end": 2.86102294921875e-06, "duration": 0.***************, "duration_str": "80.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.750792, "relative_start": 0.***************, "end": **********.753409, "relative_end": **********.753409, "duration": 0.*****************, "duration_str": "2.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.816464, "relative_start": 0.****************, "end": **********.816757, "relative_end": **********.816757, "duration": 0.0002930164337158203, "duration_str": "293μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 10, "nb_statements": 8, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00607, "accumulated_duration_str": "6.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'PzQTWPKI3YBinugcNUaYlvGxMuCwfdE7vta6hepA' limit 1", "type": "query", "params": [], "bindings": ["PzQTWPKI3YBinugcNUaYlvGxMuCwfdE7vta6hepA"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.757845, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 9.72}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.763804, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "orexcoin", "explain": null, "start_percent": 9.72, "width_percent": 10.708}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.76641, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 20.428, "width_percent": 7.578}, {"sql": "select count(*) as aggregate from `schemes` where `name` = '<PERSON><PERSON>'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 515}], "start": **********.794746, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "orexcoin", "explain": null, "start_percent": 28.007, "width_percent": 14.992}, {"sql": "select count(*) as aggregate from `miners` where `id` = '6'", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}], "start": **********.7962599, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "orexcoin", "explain": null, "start_percent": 42.998, "width_percent": 6.425}, {"sql": "select count(*) as aggregate from `schedules` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}], "start": **********.801934, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "orexcoin", "explain": null, "start_percent": 49.423, "width_percent": 15.98}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Backend/SchemeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\SchemeController.php", "line": 103}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.804639, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SchemeController.php:103", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Backend/SchemeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\SchemeController.php", "line": 103}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FSchemeController.php:103", "ajax": false, "filename": "SchemeController.php", "line": "103"}, "connection": "orexcoin", "explain": null, "start_percent": 65.404, "width_percent": 0}, {"sql": "select * from `schedules` where `schedules`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/SchemeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\SchemeController.php", "line": 115}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.805244, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "SchemeController.php:115", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/SchemeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\SchemeController.php", "line": 115}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FSchemeController.php:115", "ajax": false, "filename": "SchemeController.php", "line": "115"}, "connection": "orexcoin", "explain": null, "start_percent": 65.404, "width_percent": 5.437}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = schema() and table_name = 'schemes' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/SchemeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\SchemeController.php", "line": 105}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.807785, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "SchemeController.php:105", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/SchemeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\SchemeController.php", "line": 105}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FSchemeController.php:105", "ajax": false, "filename": "SchemeController.php", "line": "105"}, "connection": "orexcoin", "explain": null, "start_percent": 70.84, "width_percent": 29.16}, {"sql": "Rollback Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Backend/SchemeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\SchemeController.php", "line": 132}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.813044, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SchemeController.php:132", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Backend/SchemeController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\SchemeController.php", "line": 132}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FSchemeController.php:132", "ajax": false, "filename": "SchemeController.php", "line": "132"}, "connection": "orexcoin", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FAdmin.php:1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\Schedule": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FSchedule.php:1", "ajax": false, "filename": "Schedule.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://orexcoin.test/admin/schema", "action_name": "admin.schema.store", "controller_action": "App\\Http\\Controllers\\Backend\\SchemeController@store", "uri": "POST admin/schema", "controller": "App\\Http\\Controllers\\Backend\\SchemeController@store<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FSchemeController.php:70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FSchemeController.php:70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/SchemeController.php:70-141</a>", "middleware": "web, auth:admin", "duration": "299ms", "peak_memory": "40MB", "response": "Redirect to https://orexcoin.test/admin/schema/create", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1465256134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1465256134\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1714703013 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Yoko Vargasa</span>\"\n  \"<span class=sf-dump-key>miner_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"3 characters\">813</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et hic non autem est</span>\"\n  \"<span class=sf-dump-key>return_amount_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">min_max</span>\"\n  \"<span class=sf-dump-key>return_amount_value</span>\" => \"\"\n  \"<span class=sf-dump-key>return_min_amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">313</span>\"\n  \"<span class=sf-dump-key>return_max_amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">916</span>\"\n  \"<span class=sf-dump-key>return_period_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">period</span>\"\n  \"<span class=sf-dump-key>return_period</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>return_period_max_number</span>\" => \"<span class=sf-dump-str title=\"3 characters\">431</span>\"\n  \"<span class=sf-dump-key>speed_amount</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>speed</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Rhash/s</span>\"\n  \"<span class=sf-dump-key>maintenance_fee_amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>maintenance_fee_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">fixed</span>\"\n  \"<span class=sf-dump-key>max_mining_amount</span>\" => \"\"\n  \"<span class=sf-dump-key>holidays</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-05-07</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"34 characters\">&lt;p&gt;C:\\Windows\\Temp\\php57AF.tmp&lt;/p&gt;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714703013\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-991995825 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"755 characters\">declinecookies=true; acceptCookies=true; XSRF-TOKEN=eyJpdiI6IkdPckR4U2I5WTRQTFhtTGZUWVFCbUE9PSIsInZhbHVlIjoiRGtjZUhEdmNoRFFncUhCUTZ0SG1jb0tEbWo0Sk9hajMzamNNL1QyMVQ2bDBXUmlmTEFSM3I2ejVucS9SQ2ROQURxNU12eEszdDZSUHlBOXNLU0I2NG9qOHVQUDl1cUZCcDhFN1B0bC90c0JpZU1sNzhreWtXQjZEbWtsMFVUVk4iLCJtYWMiOiI0YmE0NzEzZDkyMjU0NGI0NTZlOWZiZTVkYzBkMGQ4NmIwNjU4YWI4YTczYjA0MTI3Mjc4MWViYTE4NzJmMmUxIiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6ImxWakFsVjI5cis2MXFRVm42dm4yVlE9PSIsInZhbHVlIjoiNjNOdE5iVW56RVVQeEtzQjJMeFRzaHV3Q1ZwZ2FpWG55dzFYRDNIL2Y0SkFPSWsvYVRNWjh4dEZmVUVNTm5nWFZpZkhDRjN2MlR3S2ZjNmRiZHZJdFhxOFF6N1BUVG9aVCtrUXVhTFUrbnVwSHhNWUFZTU1yT0ROamFYTmk1eEwiLCJtYWMiOiJkY2Y5Y2RjNjQ0NTRiZTU3OTgzODNhYTgzYWMxOGYzZWJmN2I0ZWZjZGYyOTJkODM3MzliOTNjMjJlYjcwMzk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">https://orexcoin.test/admin/schema/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryXE5mNgsp80cWlwse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">https://orexcoin.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">42869</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991995825\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-840332997 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>declinecookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>acceptCookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PzQTWPKI3YBinugcNUaYlvGxMuCwfdE7vta6hepA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840332997\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-352029998 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 10:18:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">https://orexcoin.test/admin/schema/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352029998\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-128600175 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">https://orexcoin.test/admin/schema/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">notify</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">_old_input</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>40</span>\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRXAG0INDSITG</span>\"\n  \"<span class=sf-dump-key>notify</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"798 characters\">SQLSTATE[HY000]: General error: 1366 Incorrect decimal value: &#039;&#039; for column &#039;max_mining_amount&#039; at row 1 (Connection: mysql, SQL: insert into `schemes` (`name`, `miner_id`, `price`, `return_amount_type`, `return_amount_value`, `return_min_amount`, `return_max_amount`, `return_period_type`, `return_period`, `return_period_hours`, `return_period_max_number`, `speed`, `speed_amount`, `max_mining_amount`, `is_featured`, `description`, `holidays`, `status`, `features`, `maintenance_fee_amount`, `maintenance_fee_type`, `icon`, `updated_at`, `created_at`) values (Yoko Vargasa, 6, 813, min_max, ?, 313, 916, period, 5, 720, 431, Rhash/s, 4, , 1, Et hic non autem est, [&quot;2025-05-07&quot;], active, [&quot;&quot;], 10, fixed, global/uploads/schema/JAzksdQUu5BoqZ7dn9Ow.jpg, 2025-06-18 16:18:45, 2025-06-18 16:18:45))</span>\"\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_old_input</span>\" => <span class=sf-dump-note>array:22</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Yoko Vargasa</span>\"\n    \"<span class=sf-dump-key>miner_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"3 characters\">813</span>\"\n    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n    \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et hic non autem est</span>\"\n    \"<span class=sf-dump-key>return_amount_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">min_max</span>\"\n    \"<span class=sf-dump-key>return_amount_value</span>\" => \"\"\n    \"<span class=sf-dump-key>return_min_amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">313</span>\"\n    \"<span class=sf-dump-key>return_max_amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">916</span>\"\n    \"<span class=sf-dump-key>return_period_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">period</span>\"\n    \"<span class=sf-dump-key>return_period</span>\" => \"<span class=sf-dump-str>5</span>\"\n    \"<span class=sf-dump-key>return_period_max_number</span>\" => \"<span class=sf-dump-str title=\"3 characters\">431</span>\"\n    \"<span class=sf-dump-key>speed_amount</span>\" => \"<span class=sf-dump-str>4</span>\"\n    \"<span class=sf-dump-key>speed</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Rhash/s</span>\"\n    \"<span class=sf-dump-key>maintenance_fee_amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n    \"<span class=sf-dump-key>maintenance_fee_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">fixed</span>\"\n    \"<span class=sf-dump-key>max_mining_amount</span>\" => \"\"\n    \"<span class=sf-dump-key>holidays</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">2025-05-07</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"\"\n    </samp>]\n    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"34 characters\">&lt;p&gt;C:\\Windows\\Temp\\php57AF.tmp&lt;/p&gt;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128600175\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://orexcoin.test/admin/schema", "action_name": "admin.schema.store", "controller_action": "App\\Http\\Controllers\\Backend\\SchemeController@store"}, "badge": "302 Found"}}