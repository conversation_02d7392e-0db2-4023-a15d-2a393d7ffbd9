{"__meta": {"id": "01JY17H7D4SDE5D6R6763TJZQR", "datetime": "2025-06-18 15:57:12", "utime": **********.229002, "method": "GET", "uri": "/user/payment-gateway-info/4?amount=95", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750240631.954492, "end": **********.229014, "duration": 0.2745218276977539, "duration_str": "275ms", "measures": [{"label": "Booting", "start": 1750240631.954492, "relative_start": 0, "end": **********.1606, "relative_end": **********.1606, "duration": 0.*****************, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.160607, "relative_start": 0.****************, "end": **********.229015, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "68.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.171976, "relative_start": 0.****************, "end": **********.173692, "relative_end": **********.173692, "duration": 0.0017158985137939453, "duration_str": "1.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::user.add_money.review_details", "start": **********.214045, "relative_start": 0.****************, "end": **********.214045, "relative_end": **********.214045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.227515, "relative_start": 0.****************, "end": **********.227767, "relative_end": **********.227767, "duration": 0.00025200843811035156, "duration_str": "252μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "frontend::user.add_money.review_details", "param_count": null, "params": [], "start": **********.214024, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/add_money/review_details.blade.phpfrontend::user.add_money.review_details", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2Fadd_money%2Freview_details.blade.php:1", "ajax": false, "filename": "review_details.blade.php", "line": "?"}}]}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00281, "accumulated_duration_str": "2.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'CtrebT5vaVz21dyXp5JHzZeSJEEk9d72NMpLDox6' limit 1", "type": "query", "params": [], "bindings": ["CtrebT5vaVz21dyXp5JHzZeSJEEk9d72NMpLDox6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.178745, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 16.37}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.1845381, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "orexcoin", "explain": null, "start_percent": 16.37, "width_percent": 23.843}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.1866488, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 40.214, "width_percent": 8.897}, {"sql": "select * from `deposit_methods` where `status` = 1 and `deposit_methods`.`id` = '4' limit 1", "type": "query", "params": [], "bindings": [1, "4"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/AddMoneyController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\AddMoneyController.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.203783, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "AddMoneyController.php:63", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/AddMoneyController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\AddMoneyController.php", "line": 63}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FAddMoneyController.php:63", "ajax": false, "filename": "AddMoneyController.php", "line": "63"}, "connection": "orexcoin", "explain": null, "start_percent": 49.11, "width_percent": 23.843}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.212871, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:67", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 67}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FProviders%2FViewServiceProvider.php:67", "ajax": false, "filename": "ViewServiceProvider.php", "line": "67"}, "connection": "orexcoin", "explain": null, "start_percent": 72.954, "width_percent": 12.456}, {"sql": "select * from `coins` where `coins`.`code` = 'BTC' limit 1", "type": "query", "params": [], "bindings": ["BTC"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Models/DepositMethod.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Models\\DepositMethod.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/AddMoneyController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\AddMoneyController.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.219378, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DepositMethod.php:71", "source": {"index": 22, "namespace": null, "name": "app/Models/DepositMethod.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Models\\DepositMethod.php", "line": 71}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FDepositMethod.php:71", "ajax": false, "filename": "DepositMethod.php", "line": "71"}, "connection": "orexcoin", "explain": null, "start_percent": 85.409, "width_percent": 14.591}]}, "models": {"data": {"App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\DepositMethod": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FDepositMethod.php:1", "ajax": false, "filename": "DepositMethod.php", "line": "?"}}, "App\\Models\\Coin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FCoin.php:1", "ajax": false, "filename": "Coin.php", "line": "?"}}}, "count": 5, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://orexcoin.test/user/payment-gateway-info/4?amount=95", "action_name": "user.paymentGateway.info", "controller_action": "App\\Http\\Controllers\\Frontend\\AddMoneyController@gatewayInfo", "uri": "GET user/payment-gateway-info/{id}", "controller": "App\\Http\\Controllers\\Frontend\\AddMoneyController@gatewayInfo<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FAddMoneyController.php:59\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FAddMoneyController.php:59\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/AddMoneyController.php:59-78</a>", "middleware": "web, auth:web, 2fa, check_deactivation, verified, check_feature:user_deposit,kyc_deposit", "duration": "275ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1852487782 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">95</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852487782\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-970978510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-970978510\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1278222798 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"755 characters\">declinecookies=true; acceptCookies=true; XSRF-TOKEN=eyJpdiI6IlhDc0d4MXYvUG04eFFiOGdKY2QwbEE9PSIsInZhbHVlIjoiLzNPSkVJcG5QRjIyU29wRHhCVmR6c0ZSZ1Zlc2orbXVSK3p1Sy9ENjU1MFNSdkpRM2NMb0ZnTFpGM3ZsbVU3c0Zjcm5tbkNIdWZibE4veStqR0JoZ05BT3VQN3lvMEhzbTgxQU9hUDRBdkYrb0J0ZkZISEZMeXZSamwwSUxDU2oiLCJtYWMiOiIyZjg4YWUzNjIyYzM5YjdiODVhM2QxYjJmMDdkYzZlY2E5NzdhZTU3ZDQzMjBiZTNhYjVjNDdiMzUyODkyYzYwIiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6Ik9oQnEvek9wMUlESlFmMGNUZlNLTEE9PSIsInZhbHVlIjoieGRwd0VMOTh5aXNhTm9wQWxOd2ZndFZLd1h2Y3JGblFGellpR3pvVkIzeGRaZXpxWjAyTnNidVlSaFFod2M0WVJCUnFPWFNQZDZZc04rd1VPVGZwQkRDZTJ2VGpaNzIrK0JmRFdLWnZlWjBYZkVNS1o5QnFxbVhIdXBxcUZEQnciLCJtYWMiOiI2ZTkzNjAxZGMzMjY5NzJjOWQyYWVkNDBhNjAyNjg4ZDFmZDRhMjQxMGViMzQ5NGU2NDU3OTEyZTMwYjUyNjUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://orexcoin.test/user/plan-purchase/checkout/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278222798\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-828352168 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>declinecookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>acceptCookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CtrebT5vaVz21dyXp5JHzZeSJEEk9d72NMpLDox6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828352168\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-940921002 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 09:57:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940921002\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-483867094 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">https://orexcoin.test/user/mining</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483867094\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://orexcoin.test/user/payment-gateway-info/4?amount=95", "action_name": "user.paymentGateway.info", "controller_action": "App\\Http\\Controllers\\Frontend\\AddMoneyController@gatewayInfo"}, "badge": null}}