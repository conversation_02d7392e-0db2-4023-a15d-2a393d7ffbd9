<?php $__env->startSection('title'); ?>
    <?php echo e($data['title']); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('meta_keywords'); ?>
    <?php echo e($data['meta_keywords']); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('meta_description'); ?>
    <?php echo e($data['meta_description']); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
   
    <?php
        $about = \App\Models\Page::where('code', 'about')->first();
        $about_data = Fluent(json_decode($about->data, true));
    ?>
    <div class="ttd-privacy-policy-section">
        <?php echo $__env->make('frontend::pages.include.breadcrumb',['title' => $about_data->title], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <div class="container section_space-top fix">
            <div class="td-page-contents">
                <?php echo $about_data->content; ?>

            </div>
        </div>
    </div>

    <!-- section  -->
    <?php if(isset($data->section_id) && $data->section_id != null): ?>
        <?php
            $section_ids = is_array(json_decode($data['section_id'])) ? json_decode($data['section_id']) : [];
            $commaIds = implode(',', $section_ids);
            $sections = \App\Models\LandingPage::whereIn('id', $section_ids)
                ->when(!blank($commaIds), function ($query) use ($commaIds) {
                    $query->orderByRaw("FIELD(id, $commaIds)");
                })
                ->where('theme', site_theme())
                ->get();
        ?>
        <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $__env->make('frontend::home.include.__' . $section->code, [
                'data' => json_decode($section->data, true),
                'content' => $section->content(),
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
    <!-- section end-->

    
               <!-- Who We Are section start -->
               <section class="td-who-we-are-section section_space_top">
                <div class="container">
                   <div class="row gy-50 align-items-center">
                      <div class="col-xxl-6 col-xl-5 col-lg-6">
                         <div class="who-we-are-thumb-wrap">
                            <div class="who-we-are-thumb has_fade_anim" data-fade-from="left">
                               <img src="<?php echo e(asset($data['who_image'])); ?>" alt="Who We thumb">
                            </div>
                         </div>
                      </div>
                      <div class="col-xxl-6 col-xl-5 col-lg-6">
                         <div class="who-we-are-contents">
                            <div class="section-title-wrapper">
                               <h2 class="section-title has_text_move_anim mb-10"><?php echo highlightColor($data['who_title']); ?></h2>
                            </div>
                            <p class="description mb-35 has_fade_anim"><?php echo e($data['who_description']); ?></p>
                            <div class="btn-inner has_fade_anim">
                               <a target="<?php echo e($data['button_target']); ?>" class="td-btn btn-chip grd-outline-fill-btn btn-secondary" href="<?php echo e($data['button_url']); ?>">
                                     <span class="inner-btn">
                                     <span class="btn-text"><?php echo e($data['button_label']); ?></span>
                                  </span>
                               </a>
                            </div>
                         </div>
                      </div>
                   </div>
                </div>
             </section>
             <!-- Who We Are section end -->

             <!-- Mining Responsibly section start -->
             <section class="td-mining-responsibly-section section_space mb-20">
                <div class="container">
                   <div class="row justify-content-center">
                      <div class="col-xxl-7 col-xl-6">
                         <div class="section-title-wrapper text-center section_title_space">
                            <h2 class="section-title has_text_move_anim mb-10"><?php echo highlightColor($data['mining_title']); ?></h2>
                            <div class="small-text has_fade_anim">
                               <small class=""><?php echo e($data['mining_subtitle']); ?></small>
                            </div>
                            <p class="mt-30 has_fade_anim"><?php echo e($data['mining_description']); ?></p>
                         </div>
                      </div>
                   </div>
                   <div class="row gy-30">
                     <?php
        $landingContent = \App\Models\LandingContent::currentTheme()->where('type', 'mining-responsibly-about-us-page')->lang()->get();
    ?>
                      <?php $__currentLoopData = $landingContent; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <div class="col-xxl-4 col-xl-4 col-lg-4 col-md-6">
                        <div class="mining-responsibly-item has_fade_anim" data-delay="0.15">
                           <div class="clip-path">
                              <div class="clip-inner">
                                 <div class="icon">
                                    <img src="<?php echo e(asset($content->icon)); ?>" alt="Mining Responsibly Icon">
                                 </div>
                                 <div class="contents">
                                    <h3 class="title"><?php echo e($content->title); ?></h3>
                                    <p class="description"><?php echo e($content->description); ?></p>
                                 </div>
                                 <div class="gradient-bg">
                                    <img src="<?php echo e(frontendAsset('images/mining-responsibly/gradient-bg.png')); ?>" alt="Gradient bg">
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                   </div>
                </div>
             </section>
             <!-- Mining Responsibly section end -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/pages/about.blade.php ENDPATH**/ ?>