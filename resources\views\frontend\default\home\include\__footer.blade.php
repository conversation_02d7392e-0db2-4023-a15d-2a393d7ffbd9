@php
    $content = fluent(json_decode(
        \App\Models\LandingPage::where('locale', app()->getLocale())
            ->where('status', true)
            ->where('theme', site_theme())
            ->where('code', 'footer')
            ->first()?->data,
        true,
    ) ?? []);

    if(!$content->toArray()){
        $content = fluent(json_decode(
            \App\Models\LandingPage::where('locale', defaultLocale())
                ->where('status', true)
                ->where('theme', site_theme())
                ->where('code', 'footer')
                ->first()?->data,
            true,
        ) ?? []);
    }
@endphp
<!-- Footer area start -->
<footer>
    <div class="td-footer-section footer-primary p-relative zi-11 fix">
        <div class="container">
            <div class="footer-main">
                <div class="row gy-50">
                    <div class="col-xxl-3 col-xl-3 col-lg-4 col-md-6 col-sm-6">
                        <!-- footer widget -->
                        <div class="footer-widget footer-col-one">
                            <div class="footer-contents">
                                <div class="footer-logo mb-25">
                                    <a href="{{ route('home') }}">
                                        <img class="logo-black" src="{{ asset(setting('site_dark_logo')) }}"
                                            alt="Footer Logo">
                                        <img class="logo-white" src="{{ asset(setting('site_logo')) }}"
                                            alt="Footer Logo">
                                    </a>
                                </div>
                                <p class="description">{{ $content['left_description'] }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-3 col-xl-2 col-lg-2 col-md-6 col-sm-6">
                        <!-- footer widget -->
                        <div class="footer-widget footer-col-two">
                            <div class="footer-wg-head">
                                <h5 class="title">{{ $content['widget_title_1'] }}</h5>
                            </div>
                            <div class="footer-links">
                                <ul>
                                    @foreach ($widget_one_menus as $menu)
                                        @if ($menu->page->status || $menu->page_id == null)
                                            <li><a href="{{ $menu->url }}">{{ $menu->tname }}</a></li>
                                        @endif
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-3 col-xl-3 col-lg-2 col-md-6 col-sm-6">
                        <!-- footer widget -->
                        <div class="footer-widget footer-col-three">
                            <div class="footer-wg-head">
                                <h5 class="title">{{ $content['widget_title_2'] }}</h5>
                            </div>
                            <div class="footer-links">
                                <ul>
                                    @foreach ($widget_two_menus as $menu)
                                        @if ($menu->page->status || $menu->page_id == null)
                                            <li><a href="{{ $menu->url }}">{{ $menu->tname }}</a></li>
                                        @endif
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-3 col-xl-4 col-lg-4 col-md-6 col-sm-6">
                        <!-- footer widget -->
                        <div class="footer-widget footer-col-four">
                            <div class="footer-wg-head">
                                <h5 class="title">{{ $content['widget_title_3'] }}</h5>
                            </div>
                            <div class="footer-info">
                                <div class="info-item">
                                    <div class="icon">
                                        <img src="{{ frontendAsset('/images/icons/call.svg') }}" alt="Call">
                                    </div>
                                    <div class="text">
                                        <p><strong>{{ __('Call') }} : </strong>
                                            <a href="tel:{{ $content['phone'] }}">{{ $content['phone'] }}</a>
                                        </p>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="icon">
                                        <img src="{{ frontendAsset('/images/icons/email.svg') }}" alt="Call">
                                    </div>
                                    <div class="text">
                                        <p><strong>{{ __('Email') }} : </strong>
                                            <a href="mailto:{{ $content['phone'] }}">{{ $content['email'] }}</a>
                                        </p>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="icon">
                                        <img src="{{ frontendAsset('/images/icons/location.svg') }}" alt="Call">
                                    </div>
                                    <div class="text">
                                        <p><strong>{{ __('Location') }} : </strong>
                                            <a href="https://maps.google.com?q={{ urlencode($content['location']) }}" target="_blank">{{ $content['location'] }}</a>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-copyright">
                <div class="copyright-text">
                    <p class="description">{{ $content['copyright_text'] }}</p>
                </div>
                <div class="footer-social">
                    <ul>
                        @foreach ($socials as $social)
                            <li>
                                <a href="{{ $social->url }}" target="_blank">
                                    <div class="bg">
                                        <div class="inner">
                                            <span>
                                                <img src="{{ asset($social->icon) }}" alt="">
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-bg" data-background="{{ frontendAsset('images/bg/footer-bg-01.png') }}"
            style="background-image: url('{{ frontendAsset('images/bg/footer-bg-01.png') }}');"></div>
    </div>
</footer>
<!-- Footer area end -->