<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" {!! when(isRtl(app()->getLocale()), 'dir="rtl"') !!}>

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>@yield('title') | {{ setting('site_title', 'global') }}</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset(setting('site_favicon', 'global')) }}">
    <!-- CSS here -->
    @include('frontend::layouts.style')
    {{-- custom style  --}}
    @stack('style')
</head>

<body class="{{ getThemeBodyClass() }}">
    {{-- notify --}}
    @include('frontend::include._notify')

    <div class="page-wrapper null compact-wrapper">
        @include('frontend::layouts.user_header')
        <div class="app-page-body-wrapper">
            @include('frontend::layouts.user_sidebar')
            <div class="app-page-body">
                <div class="row {{ $gyClass ?? 'gy-30' }}">
                    @yield('content')
                </div>
            </div>
        </div>
    </div>
    {{-- js  --}}
    @include('frontend::layouts.script')
    @push(
    'script'
    )
        <script src="{{ frontendAsset('js/sidebar-menu.js')}}"></script>
        <script src="{{ frontendAsset('js/scrollbar/simplebar.js')}}"></script>
        <script src="{{ frontendAsset('js/scrollbar/custom.js')}}"></script>
    @endpush
    
    {{-- custom js  --}}
    @stack('js')
</body>

</html>
