@extends('backend.layouts.app')
@section('title')
    {{ __('Manage Schema') }}
@endsection
@section('content')
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <div class="title-content">
                            <h2 class="title">{{ __('All Schemas') }}</h2>
                            @can('schema-create')
                                <a href="{{route('admin.schema.create')}}" class="title-btn"><i
                                            icon-name="plus-circle"></i>{{ __('Add New') }}</a>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid">
            <div class="row">
                <div class="col-xl-12">
                    <div class="site-card">
                        <div class="site-card-body">
                            <div class="site-table table-responsive">
                                <table class="table">
                                    <thead>
                                    <tr>
                                        <th scope="col">{{ __('Image') }}</th>
                                        <th scope="col">{{ __('Name') }}</th>
                                        <th scope="col">{{ __('Miner') }}</th>
                                        <th scope="col">{{ __('Price') }}</th>
                                        <th scope="col">{{ __('Return') }}</th>
                                        <th scope="col">{{ __('Speed') }}</th>
                                        <th scope="col">{{ __('Status') }}</th>
                                        <th scope="col">{{ __('Action') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @forelse($schemas as $schema)
                                        <tr>
                                            <td>
                                                <img src="{{ asset($schema->icon) }}" alt="{{ $schema->name }}" width="50" height="50">
                                            </td>
                                            <td>
                                                <strong>{{ $schema->name }}</strong>
                                                @if($schema->is_featured)
                                                    <div class="site-badge" style="background-color: {{ $schema->feature['color'] ?? '#28c76f' }}">
                                                        {{ $schema->feature['badge'] ?? 'Featured' }}
                                                    </div>
                                                @endif
                                            </td>
                                            <td>{{ $schema->miner()->exists() ? $schema->miner->name : 'N/A' }}</td>
                                            <td>{{ setting('currency_symbol', 'global') . formatAmount($schema->price, setting('site_currency_decimals', 'global')) }}</td>
                                            <td>
                                                @if($schema->return_amount_type == 'fixed')
                                                    {{  ($schema->return_amount_value.$schema->miner->coin->code) }}
                                                @else
                                                    {{  ($schema->return_min_amount.$schema->miner->coin->code) }} - 
                                                    {{  ($schema->return_max_amount.$schema->miner->coin->code) }}
                                                @endif
                                                <br>
                                                <small>
                                                    {{ $schema->return_period_type == 'period' ? 
                                                        'For ' . $schema->return_period_max_number . ' periods' : 
                                                        'Lifetime' }}
                                                </small>
                                            </td>
                                            <td>{{ number_format($schema->speed_amount, 8) . ' ' . $schema->speed }}</td>
                                            <td>
                                                <div @class([
                                                'site-badge',
                                                'success' => $schema->status == 'active',
                                                'danger' => $schema->status == 'inactive'
                                                ])>{{ $schema->status == 'active' ? 'Active' : 'Inactive' }}</div>
                                            </td>
                                            <td>
                                                @can('schema-edit')
                                                    <a href="{{route('admin.schema.edit',$schema->id)}}"
                                                       class="round-icon-btn primary-btn">
                                                        <i icon-name="edit-3"></i>
                                                    </a>
                                                @endcan
                                                @can('schema-delete')
                                                    <x-admin.delete-module-popup module="Schema" :method="'delete'" :delete-route="route('admin.schema.destroy', $schema->id)" />
                                                @endcan
                                            </td>
                                        </tr>
                                        @empty
                                        <td colspan="8" class="text-center">{{ __('No Data Found!') }}</td>
                                    @endforelse
                                    </tbody>
                                </table>
                                {{ $schemas->links() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
