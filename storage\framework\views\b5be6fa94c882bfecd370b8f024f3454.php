<?php $__env->startSection('title'); ?>
    <?php echo e(__('Blog Section')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-xl-12">
                        <div class="title-content">
                            <h2 class="title"><?php echo e(__('Blog Section')); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php echo $__env->make('backend.page.default.include.__language_bar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <div class="tab-content" id="pills-tabContent">

            <?php $__currentLoopData = $groupData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $data = new Illuminate\Support\Fluent($value);
                ?>

                <div class="tab-pane fade <?php echo e($loop->index == 0 ? 'show active' : ''); ?>" id="<?php echo e($key); ?>"
                    role="tabpanel" aria-labelledby="pills-informations-tab">
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="site-card">
                                <div class="site-card-header">
                                    <h3 class="title"><?php echo e(__('Contents')); ?></h3>
                                </div>
                                <div class="site-card-body">
                                    <form action="<?php echo e(route('admin.page.section.section.update')); ?>" method="post"
                                        enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="section_code" value="blog">
                                        <input type="hidden" name="section_locale" value="<?php echo e($key); ?>">

                                        <?php if($key == 'en'): ?>
                                            <div class="site-input-groups row">
                                                <label for=""
                                                    class="col-sm-2 col-label pt-0"><?php echo e(__('Section Activity')); ?><i
                                                        icon-name="info" data-bs-toggle="tooltip" title=""
                                                        data-bs-original-title="Manage Section Visibility"></i></label>
                                                <div class="col-sm-3">
                                                    <div class="site-input-groups">
                                                        <div class="switch-field">
                                                            <input type="radio" id="active" name="status"
                                                                <?php if($status): ?> checked <?php endif; ?>
                                                                value="1" />
                                                            <label for="active"><?php echo e(__('Show')); ?></label>
                                                            <input type="radio" id="deactivate" name="status"
                                                                <?php if(!$status): ?> checked <?php endif; ?>
                                                                value="0" />
                                                            <label for="deactivate"><?php echo e(__('Hide')); ?></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <div class="site-input-groups row">
                                            <label for="" class="col-sm-2 col-label"><?php echo e(__('Blog Title')); ?></label>
                                            <div class="col-sm-10">
                                                <input type="text" name="blog_title" class="box-input"
                                                    value="<?php echo e($data->blog_title); ?>">
                                                <small class="text-muted">
                                                    <?php echo e(__('Use this shortcode to highlight words. Example: [[color_text= Your Text Here ]]')); ?>

                                                </small>
                                            </div>
                                        </div>
                                        <div class="site-input-groups row">
                                            <label for=""
                                                class="col-sm-2 col-label"><?php echo e(__('Blog Subtitle')); ?></label>
                                            <div class="col-sm-10">
                                                <input type="text" name="blog_subtitle" class="box-input"
                                                    value="<?php echo e($data->blog_subtitle); ?>">
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="offset-sm-2 col-sm-10">
                                                <button type="submit"
                                                    class="site-btn-sm primary-btn w-100"><?php echo e(__('Save Changes')); ?></button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div class="section-design-nb">
            <strong>NB:</strong>All Blog will come from <a href="<?php echo e(route('admin.page.edit', 'blog')); ?>">Pages > Blog</a>
            page
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/page/default/section/blog.blade.php ENDPATH**/ ?>