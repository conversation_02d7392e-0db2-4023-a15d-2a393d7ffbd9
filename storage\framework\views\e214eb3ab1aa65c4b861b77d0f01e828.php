<?php if($kyc->status == 'approved'): ?>
    <ul>
        <li>
            <span class="text">
                <?php echo e(__('Status:')); ?>

            </span>
            <span class="td-badge badge-success">
                <?php echo e(__('Approved')); ?>

            </span>
        </li>
    </ul>
<?php elseif($kyc->status == 'rejected'): ?>
    <ul>
        <li>
            <span class="text">
                <?php echo e(__('Status:')); ?>

            </span>
            <span class="td-badge badge-danger">
                <?php echo e(__('Rejected')); ?>

            </span>
        </li>
    </ul>
<?php elseif($kyc->status == 'pending'): ?>
    <ul>
        <li>
            <span class="text">
                <?php echo e(__('Status:')); ?>

            </span>
            <span class="td-badge badge-warning">
                <?php echo e(__('Pending')); ?>

            </span>
        </li>
    </ul>
<?php endif; ?>
<ul>
    <li>
        <span class="text">
            <?php echo e(__('Submission Date:')); ?>

        </span>
        <span class="text-highlight">
            <?php echo e(date('d M Y h:i A', strtotime($kyc->created_at))); ?>

        </span>
    </li>
</ul>

<?php if($kyc->message): ?>
<ul>
    <li>
        <span class="text">
            <?php echo e(__('Message from admin:')); ?>

        </span>
        <span class="text-highlight">
            <?php echo e($kyc->message); ?>

        </span>
    </li>
</ul>
<?php endif; ?>

<?php $__currentLoopData = $kyc->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <ul>
        <li>
            <span class="text"><?php echo e($key); ?>:</span>
            <span class="text-highlight">
                <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|bmp)$/i', $value)): ?>
                    <a href="<?php echo e(asset($value)); ?>" target="_blank" rel="noopener noreferrer"><?php echo e(__('Click to view')); ?></a>
                <?php else: ?>
                    <?php echo e($value); ?>

                <?php endif; ?>
            </span>
        </li>
    </ul>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/kyc/include/__kyc_details_modal.blade.php ENDPATH**/ ?>