<h3 class="title mb-4">
    {{ __('Deposit Approval Action') }}
</h3>
@use('App\Enums\TxnType')

<ul class="list-group mb-4">
    <li class="list-group-item">
        {{ $data?->user?->role . ' Name :' }}
        <strong>
            {{ $data?->user?->first_name . ' ' . $data?->user?->last_name }}
        </strong>
    </li>
    <li class="list-group-item">
        {{ __('Total amount :') }} <strong>
            @if (in_array($data->type, [TxnType::PlanPurchase->value, TxnType::PlanPurchaseManual->value]))
            {{ formatAmount($data->final_amount).' '.$currency }}</strong>
                @else
                {{ trxAmountFormat($data, 'final_amount') }}</strong>
            @endif
    </li>
    <li class="list-group-item">
        {{ __('Charge :') }} <strong>{{ trxAmountFormat($data, 'charge') }}</strong>
    </li>
    <li class="list-group-item">
        {{ __('Paid Amount :') }} <strong>{{ trxAmountFormat($data, 'pay_amount') }}</strong>
    </li>
    <li class="list-group-item">
        {{ __('Wallet :') }}
        <strong>
            @if ($data?->wallet_type == null || $data?->wallet_type == 'default')
                {{ __('Main Wallet') }}
            @else
                {{ $data?->wallet?->coin?->name }}
            @endif
        </strong>
    </li>
    <li class="list-group-item">
        {{ __('Transaction Type :') }}
        <strong class="site-badge primary">
            {{ $data->type_text }}
        </strong>
    </li>
</ul>

<ul class="list-group mb-4">
    @foreach ($data->manual_field_data as $key => $value)
        <li class="list-group-item">
            {{ $key }}:
            @if ($value != new stdClass())
                @if (file_exists(public_path('' . $value)))
                    <a target="__blank" href="{{ asset($value) }}">
                        <img src="{{ asset($value) }}" alt="{{ $key }}" />
                    </a>
                @else
                    <strong>{{ $value }}</strong>
                @endif
            @endif
        </li>
    @endforeach
</ul>
<form action="{{ route('admin.deposit.action.now') }}" method="post">
    @csrf
    <input type="hidden" name="id" value="{{ $id }}">

    <div class="site-input-groups">
        <label for="" class="box-input-label">{{ __('Details Message(Optional)') }}</label>
        <textarea name="message" class="form-textarea mb-0" placeholder="Details Message"></textarea>
    </div>

    <div class="action-btns">
        <button type="submit" name="approve" value="yes" class="site-btn-sm primary-btn me-2">
            <i data-lucide="check"></i>
            {{ __('Approve') }}
        </button>
        <button type="submit" name="reject" value="yes" class="site-btn-sm red-btn">
            <i data-lucide="x"></i>
            {{ __('Reject') }}
        </button>
    </div>
</form>
<script>
    'use strict';
    lucide.createIcons();
</script>