<?php $__env->startSection('title'); ?>
    <?php echo e($blog->title); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Breadcrumb area start -->
    <!-- Breadcrumb area end -->
    <!-- Postbox details area start -->
    <section class="td-postbox-area p-relative zi-11 section_space">
        <div class="container">
            <div class="postbox-wrapper">
                <div class="row gy-50">
                    <!-- Main Content Column -->
                    <div class="col-xxl-8 col-xl-8 col-lg-7">
                        <div class="postbox-main-wrapper has_fade_anim">
                            <div class="postbox-details-contents">
                                <h2><?php echo e($blog->title); ?></h2>
                                <p><?php echo e($blog->short_description); ?></p>

                                <!-- Blog Image -->
                                <div class="postbox-thumb-inner">
                                    <div class="postbox-img">
                                        <img src="<?php echo e(asset($blog->cover)); ?>" alt="<?php echo e($blog->title); ?>" class="my-0">
                                    </div>
                                </div>

                                <!-- Meta Info -->
                                <div class="postbox-meta">
                                    <ul>
                                        <li>
                                            <span class="icon"><iconify-icon
                                                    icon="tabler:calendar-week-filled"></iconify-icon></span>
                                            <span class="text">Published on
                                                <?php echo e(\Carbon\Carbon::parse($blog->created_at)->format('F j, Y')); ?></span>
                                        </li>
                                        <li>
                                            <span class="icon"><iconify-icon icon="tabler:pencil"></iconify-icon></span>
                                            <span class="text">By Admin</span>
                                        </li>
                                        <li>
                                            <span class="icon"><iconify-icon icon="tabler:clock"></iconify-icon></span>
                                            <span
                                                class="text"><?php echo e(\Carbon\Carbon::parse($blog->updated_at)->diffForHumans()); ?></span>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Blog Content -->
                                <div class="postbox-text mt-30">
                                    <?php echo $blog->details; ?>

                                </div>

                                <!-- Social Sharing -->
                                <div class="postbox-share mt-40">
                                    <h6>Share this post:</h6>
                                    <a href="<?php echo e(socialMediaShareLinks(route('blog.details', $blog->id), 'facebook')); ?>"
                                        target="_blank">
                                        <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M6.182 10.833C5.204 10.833 5 11.025 5 11.944V13.611C5 14.531 5.204 14.722 6.182 14.722H8.545V21.389C8.545 22.309 8.75 22.5 9.727 22.5H12.091C13.069 22.5 13.273 22.308 13.273 21.389V14.722H15.927C16.668 14.722 16.859 14.587 17.063 13.916L17.57 12.25C17.919 11.101 17.703 10.833 16.433 10.833H13.273V8.056C13.273 7.442 13.802 6.944 14.454 6.944H17.818C18.796 6.944 19 6.753 19 5.834V3.61C19 2.691 18.796 2.5 17.818 2.5H14.454C11.191 2.5 8.545 4.987 8.545 8.056V10.833h5.182Z"
                                                stroke="#9A9DA7" stroke-width="1.5" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                    </a>
                                    <a href="<?php echo e(socialMediaShareLinks(route('blog.details', $blog->id), 'twitter')); ?>"
                                        target="_blank">
                                        <svg width="20" height="21" viewBox="0 0 20 21" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M1 19.5L8.548 11.952M8.548 11.952L1 1.5H6L11.452 9.048M8.548 11.952L14 19.5H19L11.452 9.048M19 1.5L11.452 9.048"
                                                stroke="#9A9DA7" stroke-width="1.5" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                    </a>
                                    <a href="<?php echo e(socialMediaShareLinks(route('blog.details', $blog->id), 'linkedin')); ?>"
                                        target="_blank">
                                        <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M12.326 10H11.5C10.557 10 10.086 10 9.793 10.293C9.5 10.586 9.5 11.057 9.5 12V20.5C9.5 21.443 9.5 21.914 9.793 22.207C10.086 22.5 10.557 22.5 11.5 22.5H12C12.943 22.5 13.414 22.5 13.707 22.207C14 21.914 14 21.443 14 20.5V17C14 15.343 14.528 14 16.088 14C16.868 14 17.5 14.672 17.5 15.5V20C17.5 20.943 17.5 21.414 17.793 21.707C18.086 22 18.557 22 19.5 22H19.999C20.941 22 21.413 22 21.706 21.707C21.998 21.414 21.999 20.943 21.999 20.001L22 14.5C22 12.014 19.636 10 17.297 10C15.965 10 14.777 10.652 14 11.673C14 11.043 14 10.728 13.863 10.494C13.7764 10.3459 13.6531 10.2226 13.505 10.136C13.271 9.999 12.956 9.999 12.326 9.999M4.5 10H4C3.057 10 2.586 10 2.293 10.293C2 10.586 2 11.057 2 12V20.5C2 21.443 2 21.914 2.293 22.207C2.586 22.5 3.057 22.5 4 22.5H4.5C5.443 22.5 5.914 22.5 6.207 22.207C6.5 21.914 6.5 21.443 6.5 20.5V12C6.5 11.057 6.5 10.586 6.207 10.293C5.914 10 5.443 10 4.5 10ZM6.5 4.75C6.5 5.34674 6.26295 5.91903 5.84099 6.34099C5.41903 6.76295 4.84674 7 4.25 7C3.65326 7 3.08097 6.76295 2.65901 6.34099C2.23705 5.91903 2 5.34674 2 4.75C2 4.15326 2.23705 3.58097 2.65901 3.15901C3.08097 2.73705 3.65326 2.5 4.25 2.5C4.84674 2.5 5.41903 2.73705 5.84099 3.15901C6.26295 3.58097 6.5 4.15326 6.5 4.75Z"
                                                stroke="#9A9DA7" stroke-width="1.5" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar Column -->
                    <div class="col-xxl-4 col-xl-4 col-lg-5">
                        <div class="sidebar-widgets-wrapper has_fade_anim">
                            <div class="sidebar-widget-inner sidebar-sticky">

                                <!-- Recent Posts -->
                                <div class="sidebar-widget">
                                    <h3 class="sidebar-widget-title">Recent Posts</h3>
                                    <div class="sidebar-widget-content-box">
                                        <div class="sidebar-posts">
                                            <?php $__currentLoopData = $recentBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="rc-post d-flex align-items-center">
                                                    <div class="rc-post-thumb-clip">
                                                        <div class="rc-post-thumb">
                                                            <a href="<?php echo e(route('blog.details', $recent->id)); ?>">
                                                                <img src="<?php echo e(asset($recent->cover)); ?>"
                                                                    alt="<?php echo e($recent->title); ?>">
                                                            </a>
                                                        </div>
                                                    </div>
                                                    <div class="rc-post-content">
                                                        <h3 class="rc-post-title">
                                                            <a href="<?php echo e(route('blog.details', $recent->id)); ?>">
                                                                <?php echo e(Str::limit($recent->title, 40)); ?>

                                                            </a>
                                                        </h3>
                                                        <div class="rc-meta">
                                                            <span class="icon"><iconify-icon
                                                                    icon="tabler:pencil"></iconify-icon></span>
                                                            <span class="text">
                                                                <?php echo e(\Carbon\Carbon::parse($recent->created_at)->format('F j, Y')); ?>

                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tags Widget -->
                                <div class="sidebar-widget">
                                    <h3 class="sidebar-widget-title">Tags</h3>
                                    <div class="tagcloud-items">
                                        <div class="tagcloud-box">
                                            <?php $__currentLoopData = explode(',',$blog->tags ?? []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <a href="#"><?php echo e($tag); ?></a>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Postbox details area end -->

<?php $__env->stopSection(); ?>
<?php echo $__env->make('frontend::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/pages/blog_details.blade.php ENDPATH**/ ?>