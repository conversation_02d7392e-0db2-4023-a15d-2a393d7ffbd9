<?php

namespace App\Http\Controllers\Backend;

use App\Enums\MinerStatus;
use App\Http\Controllers\Controller;
use App\Models\Coin;
use App\Models\Miner;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class MinerController extends Controller implements HasMiddleware
{
    public static function middleware()
    {
        return [
            new Middleware('permission:miner-list', ['only' => ['index']]),
            new Middleware('permission:miner-create', ['only' => ['create', 'store']]),
            new Middleware('permission:miner-edit', ['only' => ['edit', 'update']]),
            new Middleware('permission:miner-delete', ['only' => ['delete']]),
        ];
    }

    public function __construct()
    {
        $this->hashrates = config('speed');
    }

    public $hashrates;

    public function index()
    {
        $miners = Miner::with('coin')->latest()->get();

        return view('backend.miner.index', compact('miners'));
    }

    public function create()
    {
        $coins = Coin::all();
        $hashrates = $this->hashrates;

        return view('backend.miner.create', compact('coins', 'hashrates'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'coin_id' => 'required|exists:coins,id',
            'renewable_energy' => 'required|numeric|min:1|max:100',
            'uptime' => 'required|numeric|min:1|max:100',
            'status' => 'required|in:active,inactive',
            'network_hashrate' => 'required|string',
            'network_hashrate_amount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            Miner::create([
                'name' => $request->name,
                'coin_id' => $request->coin_id,
                'renewable_energy' => $request->renewable_energy,
                'uptime' => $request->uptime,
                'status' => $request->status === 'active' ? MinerStatus::Active : MinerStatus::Inactive,
                'network_hashrate' => $request->network_hashrate,
                'network_hashrate_amount' => $request->network_hashrate_amount,
            ]);

            DB::commit();

            notify()->success(__('Miner created successfully!'));

            return redirect()->route('admin.miner.index');
        } catch (\Throwable $throwable) {
            throw $throwable;
            DB::rollBack();
            notify()->error(__('Sorry! Something went wrong. Please try again.'));

            return redirect()->back();
        }
    }

    public function edit($id)
    {
        $miner = Miner::findOrFail($id);
        $coins = Coin::all();
        $hashrates = $this->hashrates;

        return view('backend.miner.edit', compact('miner', 'coins', 'hashrates'));
    }

    public function update(Request $request, $id)
    {
        $miner = Miner::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'coin_id' => 'required|exists:coins,id',
            'renewable_energy' => 'required|numeric|min:1|max:100',
            'uptime' => 'required|numeric|min:1|max:100',
            'status' => 'required|in:active,inactive',
            'network_hashrate' => 'required|string',
            'network_hashrate_amount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $miner->update([
                'name' => $request->name,
                'coin_id' => $request->coin_id,
                'renewable_energy' => $request->renewable_energy,
                'uptime' => $request->uptime,
                'status' => $request->status,
                'network_hashrate' => $request->network_hashrate,
                'network_hashrate_amount' => $request->network_hashrate_amount,
            ]);

            DB::commit();

            notify()->success(__('Miner updated successfully!'));

            return redirect()->route('admin.miner.index');
        } catch (\Throwable $throwable) {
            DB::rollBack();
            notify()->error(__('Sorry! Something went wrong. Please try again.'));

            return redirect()->back();
        }
    }

    public function delete($id)
    {
        try {
            $miner = Miner::withCount('scheme')->findOrFail($id);
            if ($miner->schemes_count > 0) {
                notify()->error(__('Miner is used in scheme. You can not delete it!'));
                return redirect()->back();
            }
            $miner->delete();

            notify()->success(__('Miner deleted successfully!'));

            return redirect()->route('admin.miner.index');
        } catch (\Throwable $throwable) {
            notify()->error(__('Sorry! Something went wrong. Please try again.'));

            return redirect()->back();
        }
    }
}
