<?php
    $content = fluent(json_decode(
        \App\Models\LandingPage::where('locale', app()->getLocale())
            ->where('status', true)
            ->where('theme', site_theme())
            ->where('code', 'footer')
            ->first()?->data,
        true,
    ) ?? []);

    if(!$content->toArray()){
        $content = fluent(json_decode(
            \App\Models\LandingPage::where('locale', defaultLocale())
                ->where('status', true)
                ->where('theme', site_theme())
                ->where('code', 'footer')
                ->first()?->data,
            true,
        ) ?? []);
    }
?>
<!-- Footer area start -->
<footer>
    <div class="td-footer-section footer-primary p-relative zi-11 fix">
        <div class="container">
            <div class="footer-main">
                <div class="row gy-50">
                    <div class="col-xxl-3 col-xl-3 col-lg-4 col-md-6 col-sm-6">
                        <!-- footer widget -->
                        <div class="footer-widget footer-col-one">
                            <div class="footer-contents">
                                <div class="footer-logo mb-25">
                                    <a href="<?php echo e(route('home')); ?>">
                                        <img class="logo-black" src="<?php echo e(asset(setting('site_dark_logo'))); ?>"
                                            alt="Footer Logo">
                                        <img class="logo-white" src="<?php echo e(asset(setting('site_logo'))); ?>"
                                            alt="Footer Logo">
                                    </a>
                                </div>
                                <p class="description"><?php echo e($content['left_description']); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-3 col-xl-2 col-lg-2 col-md-6 col-sm-6">
                        <!-- footer widget -->
                        <div class="footer-widget footer-col-two">
                            <div class="footer-wg-head">
                                <h5 class="title"><?php echo e($content['widget_title_1']); ?></h5>
                            </div>
                            <div class="footer-links">
                                <ul>
                                    <?php $__currentLoopData = $widget_one_menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($menu->page->status || $menu->page_id == null): ?>
                                            <li><a href="<?php echo e($menu->url); ?>"><?php echo e($menu->tname); ?></a></li>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-3 col-xl-3 col-lg-2 col-md-6 col-sm-6">
                        <!-- footer widget -->
                        <div class="footer-widget footer-col-three">
                            <div class="footer-wg-head">
                                <h5 class="title"><?php echo e($content['widget_title_2']); ?></h5>
                            </div>
                            <div class="footer-links">
                                <ul>
                                    <?php $__currentLoopData = $widget_two_menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($menu->page->status || $menu->page_id == null): ?>
                                            <li><a href="<?php echo e($menu->url); ?>"><?php echo e($menu->tname); ?></a></li>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-3 col-xl-4 col-lg-4 col-md-6 col-sm-6">
                        <!-- footer widget -->
                        <div class="footer-widget footer-col-four">
                            <div class="footer-wg-head">
                                <h5 class="title"><?php echo e($content['widget_title_3']); ?></h5>
                            </div>
                            <div class="footer-info">
                                <div class="info-item">
                                    <div class="icon">
                                        <img src="<?php echo e(frontendAsset('/images/icons/call.svg')); ?>" alt="Call">
                                    </div>
                                    <div class="text">
                                        <p><strong><?php echo e(__('Call')); ?> : </strong>
                                            <a href="tel:<?php echo e($content['phone']); ?>"><?php echo e($content['phone']); ?></a>
                                        </p>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="icon">
                                        <img src="<?php echo e(frontendAsset('/images/icons/email.svg')); ?>" alt="Call">
                                    </div>
                                    <div class="text">
                                        <p><strong><?php echo e(__('Email')); ?> : </strong>
                                            <a href="mailto:<?php echo e($content['phone']); ?>"><?php echo e($content['email']); ?></a>
                                        </p>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="icon">
                                        <img src="<?php echo e(frontendAsset('/images/icons/location.svg')); ?>" alt="Call">
                                    </div>
                                    <div class="text">
                                        <p><strong><?php echo e(__('Location')); ?> : </strong>
                                            <a href="https://maps.google.com?q=<?php echo e(urlencode($content['location'])); ?>" target="_blank"><?php echo e($content['location']); ?></a>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-copyright">
                <div class="copyright-text">
                    <p class="description"><?php echo e($content['copyright_text']); ?></p>
                </div>
                <div class="footer-social">
                    <ul>
                        <?php $__currentLoopData = $socials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e($social->url); ?>" target="_blank">
                                    <div class="bg">
                                        <div class="inner">
                                            <span>
                                                <img src="<?php echo e(asset($social->icon)); ?>" alt="">
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-bg" data-background="<?php echo e(frontendAsset('images/bg/footer-bg-01.png')); ?>"
            style="background-image: url('<?php echo e(frontendAsset('images/bg/footer-bg-01.png')); ?>');"></div>
    </div>
</footer>
<!-- Footer area end --><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/home/<USER>/__footer.blade.php ENDPATH**/ ?>