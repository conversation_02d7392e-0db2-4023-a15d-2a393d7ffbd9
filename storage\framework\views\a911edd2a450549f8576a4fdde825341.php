<?php $__env->startSection('title'); ?>
    <?php echo e(__('Transactions')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <div class="title-content">
                            <h2 class="title"><?php echo e(__('Transactions')); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-xl-12">
                    <div class="site-table table-responsive">
                        <?php echo $__env->make('backend.transaction.include.__filter', [
                            'status' => true,
                            'type' => true,
                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <table class="table">
                            <thead>
                                <tr>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'Date',
                                        'field' => 'created_at',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', ['label' => 'User', 'field' => 'user'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'Transaction ID',
                                        'field' => 'tnx',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', ['label' => 'Type', 'field' => 'type'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'Amount',
                                        'field' => 'amount',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'Charge',
                                        'field' => 'charge',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'Gateway',
                                        'field' => 'method',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <?php echo $__env->make('backend.filter.th', [
                                        'label' => 'Status',
                                        'field' => 'status',
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <?php echo e($transaction->created_at); ?>

                                        </td>
                                        <td>
                                            <?php echo $__env->make('backend.transaction.include.__user', [
                                                'id' => $transaction->user_id,
                                                'name' => $transaction->user->username,
                                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        </td>
                                        <td><?php echo e(safe($transaction->tnx)); ?></td>
                                        <td>
                                            <?php echo $__env->make('backend.transaction.include.__txn_type', [
                                                'txnType' => $transaction->type,
                                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        </td>
                                        <td>
                                            <?php echo e(formatAmount($transaction->amount, $transaction->wallet?->coin, true)); ?>

                                        </td>
                                        <td><?php echo e(formatAmount($transaction->charge, $transaction->wallet?->coin, true)); ?></td>
                                        <td>
                                            <?php echo e(safe($transaction->method)); ?>

                                        </td>
                                        <td>
                                            <?php echo $__env->make('backend.transaction.include.__txn_status', [
                                                'status' => $transaction->status->value,
                                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <td colspan="7" class="text-center"><?php echo e(__('No Data Found!')); ?></td>
                                <?php endif; ?>
                            </tbody>
                        </table>

                        <?php echo e($transactions->links('backend.include.__pagination')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/transaction/index.blade.php ENDPATH**/ ?>