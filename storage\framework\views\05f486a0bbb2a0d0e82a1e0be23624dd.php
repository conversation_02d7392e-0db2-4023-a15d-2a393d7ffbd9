<?php $__env->startSection('title'); ?>
    <?php echo e(__('Footer Contents')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-xl-12">
                        <div class="title-content">
                            <h2 class="title"><?php echo e(__('Footer Contents')); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php echo $__env->make('backend.page.default.include.__language_bar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <div class="tab-content" id="pills-tabContent">
 
            <?php $__currentLoopData = $groupData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $data = new Illuminate\Support\Fluent($value);
                ?>

                <div class="tab-pane fade <?php echo e($loop->index == 0 ? 'show active' : ''); ?>" id="<?php echo e($key); ?>"
                    role="tabpanel" aria-labelledby="pills-informations-tab">

                    <div class="site-card">
                        <div class="site-card-header">
                            <h3 class="title"><?php echo e(__('Image and Widget Contents')); ?></h3>
                        </div>
                        <div class="site-card-body">
                            <form action="<?php echo e(route('admin.page.section.section.update')); ?>" method="post"
                                enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="section_code" value="footer">
                                <input type="hidden" name="section_locale" value="<?php echo e($key); ?>">

                                <div class="site-input-groups row">
                                    <label for=""
                                        class="col-sm-3 col-label"><?php echo e(__('Widget Middle 1 Title')); ?></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="widget_title_1" class="box-input"
                                            value="<?php echo e($data->widget_title_1); ?>">
                                    </div>
                                </div>
                                <div class="site-input-groups row">
                                    <label for=""
                                        class="col-sm-3 col-label"><?php echo e(__('Widget Middle 2 Title')); ?></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="widget_title_2" class="box-input"
                                            value="<?php echo e($data->widget_title_2); ?>">
                                    </div>
                                </div>
                                <div class="site-input-groups row">
                                    <label for=""
                                        class="col-sm-3 col-label"><?php echo e(__('Widget Middle 3 Title')); ?></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="widget_title_3" class="box-input"
                                            value="<?php echo e($data->widget_title_3); ?>">
                                    </div>
                                </div>
                                <div class="site-input-groups row">
                                    <label for="" class="col-sm-3 col-label"><?php echo e(__('Description')); ?></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="left_description" class="box-input"
                                            value="<?php echo e($data->left_description); ?>">
                                    </div>
                                </div>
                                <div class="site-input-groups row">
                                    <label for="" class="col-sm-3 col-label"><?php echo e(__('Contact Info')); ?></label>
                                    <div class="col-sm-3">
                                        <input placeholder="<?php echo e(__('Phone')); ?>" type="text" name="phone" class="box-input"
                                            value="<?php echo e($data->phone); ?>">
                                    </div>
                                    <div class="col-sm-3">
                                        <input placeholder="<?php echo e(__('Email')); ?>" type="text" name="email" class="box-input"
                                            value="<?php echo e($data->email); ?>">
                                    </div>
                                    <div class="col-sm-3">
                                        <input placeholder="<?php echo e(__('Location')); ?>" type="text" name="location" class="box-input"
                                            value="<?php echo e($data->location); ?>">
                                    </div>
                                </div>
                                
                                <div class="site-input-groups row">
                                    <label for="" class="col-sm-3 col-label"><?php echo e(__('Copyright Text')); ?><i
                                            data-lucide="info" data-bs-toggle="tooltip" title=""
                                            data-bs-original-title="Change the Copyright Text"></i></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="copyright_text" class="box-input"
                                            value="<?php echo e($data->copyright_text); ?>">
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="offset-sm-3 col-sm-9">
                                        <div class="paragraph"><i data-lucide="alert-triangle"></i><?php echo e(__('All the')); ?>

                                            <strong><?php echo e(__('Footer Menus')); ?></strong> <?php echo e(__('will come from')); ?> <a
                                                href="<?php echo e(route('admin.navigation.footer')); ?>"
                                                class="link"><?php echo e(__('Menu Management')); ?></a>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="offset-sm-3 col-sm-9">
                                        <button type="submit"
                                            class="site-btn-sm primary-btn"><?php echo e(__('Save Changes')); ?></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <div class="site-card">
                <div class="site-card-header">
                    <h3 class="title"><?php echo e(__('Social Icons')); ?></h3>
                    <div class="card-header-links">
                        <a href="" class="card-header-link" type="button" data-bs-toggle="modal"
                            data-bs-target="#addNew"><?php echo e(__('Add New')); ?></a>
                    </div>
                </div>

                <form action="<?php echo e(route('admin.social.position.update')); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <div class="site-card-body">
                        <p class="paragraph"><i data-lucide="alert-triangle"></i><?php echo e(__('All the')); ?>

                            <strong><?php echo e(__('Social Icons are Draggable.')); ?></strong> <?php echo e(__('Once you drag then click')); ?>

                            <strong><?php echo e(__('Save Changes')); ?></strong>
                        </p>
                        <div class="site-table table-responsive mb-0">
                            <table class="table mb-0" id="sortable">
                                <thead>
                                    <tr>
                                        <th scope="col"><?php echo e(__('Icon')); ?></th>
                                        <th scope="col"><?php echo e(__('URL')); ?></th>
                                        <th scope="col"><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $socials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <input type="hidden" name="<?php echo e($loop->index); ?>"
                                                value="<?php echo e($social->id); ?>">
                                            <td>
                                                <img class="avatar avatar-round" src="<?php echo e(asset($social->icon)); ?>">
                                            </td>
                                            <td><?php echo e($social->url); ?></td>
                                            <td>
                                                <button class="round-icon-btn primary-btn editContent" type="button"
                                                    data-content="<?php echo e($social); ?>"
                                                    data-icon="<?php echo e(asset($social->icon)); ?>">
                                                    <i data-lucide="edit-3"></i>
                                                </button>
                                                <button class="round-icon-btn red-btn deleteContent" type="button"
                                                    data-id="<?php echo e($social->id); ?>">
                                                    <i data-lucide="trash-2"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="site-card-footer">
                        <button type="submit" class="site-btn-sm primary-btn"><?php echo e(__('Save Changes')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- Modal for Add New  -->
    <?php echo $__env->make('backend.page.' . site_theme() . '.section.include.__add_new_social', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Add New How It Works End -->

    <!-- Modal for Edit -->
    <?php echo $__env->make('backend.page.' . site_theme() . '.section.include.__edit_social', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Edit  End-->

    <!-- Modal for Delete  -->
    <?php echo $__env->make('backend.page.' . site_theme() . '.section.include.__delete_social', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Delete  End-->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    <script>
        $(function() {
            'use strict';
            $("#sortable tbody").sortable({
                cursor: "move",
                placeholder: "sortable-placeholder",
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        // Set helper cell sizes to match the original sizes
                        $(this).width($originals.eq(index).width());
                    });
                    return $helper;
                }
            }).disableSelection();
        });
        $('.editContent').on('click', function(e) {
            "use strict";
            e.preventDefault();
            var content = $(this).data('content');
            var icon = $(this).data('icon');

            $('#updatedId').val(content.id);
            $('.url').val(content.url);
            $('.file-ok').css('background-image', 'url(' + icon + ')');
            $('#editContent').modal('show');
            imagePreview();
        });

        $('.deleteContent').on('click', function(e) {
            "use strict";
            e.preventDefault();
            var id = $(this).data('id');
            $('#deleteId').val(id);
            $('#deleteContent').modal('show');
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/page/default/section/footer.blade.php ENDPATH**/ ?>