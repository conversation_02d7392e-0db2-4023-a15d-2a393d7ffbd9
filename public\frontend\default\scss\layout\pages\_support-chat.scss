@use '../../utils' as *;

/*----------------------------------------*/
/* Support chat styles
/*----------------------------------------*/
.support-ticket-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #d5dae7;
    padding: 12px 16px;
    border-radius: 10px;
    flex-wrap: wrap;
    gap: 12px 12px;

    @include dark-theme {
        background: #16213F;
    }
}

.support-ticket-top-right {
    display: flex;
    align-items: center;
    gap: 12px 12px;
    flex-wrap: wrap;
}

.support-ticket-chat-area {
    border-radius: 1rem;
    border: 1px solid #0B277A;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
    border: 1px solid rgba($heading, $alpha: 0.16);
    padding: 30px 30px;

    @include dark-theme {
        border-color: rgba($white, $alpha: 0.16);
    }

    @media #{$xs,$md,$lg} {
        padding: 30px 30px;
    }

    @media #{$xxs} {
        padding: 24px 24px;
    }
}

.support-box-wrapper {

    .support-chat-text-item {
        @include flexbox();
        gap: 16px 16px;
        justify-content: start;
        width: 45%;

        @media #{$xl,$lg} {
            width: 75%;
        }

        @media #{$md} {
            width: 85%;
        }


        @media #{$xs,$sm} {
            width: 100%;
        }

        &:not(:last-child) {
            margin-bottom: 20px;
        }

        &.user-message {
            flex-direction: row-reverse;
            margin-inline-start: auto;
            justify-content: end;

            .message-list-box {
                .message-list {
                    .description {
                        color: var(--td-text-primary)
                    }
                }
            }
        }
    }

    .chat-text-avatar {
        flex: 0 0 auto;

        .thumb {
            width: 40px;
            height: 40px;

            @media #{$xs} {
                width: 32px;
                height: 32px;
            }

            span {
                width: 40px;
                height: 40px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                background-color: #7879F1;
                border-radius: 50%;
                font-size: 26px;
                color: var(--td-white);

                @media #{$xs} {
                    width: 32px;
                    height: 32px;
                }
            }

            img {
                width: 100%;
            }
        }
    }

    .message-list-box {

        .message-list {
            padding: 12px 12px;
            position: relative;
            background-color: rgba(34, 34, 35, 0.04);
            border: 1px solid rgba(34, 34, 35, 0.10);
            border-radius: 10px;

            @include dark-theme {
                background-color: rgba(255, 255, 255, 0.04);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .description {
                line-height: 28px;

                @include dark-theme {
                    color: rgba($white, $alpha: 1);
                }

                @media #{$xs} {
                    font-size: 14px;
                }
            }

            .chat-attachments {
                @include flexbox();
                flex-wrap: wrap;
                gap: 5px;
                margin-top: 5px;

                .attachment {
                    padding: 12px 16px;
                    font-size: 14px;
                    @include flexbox();
                    align-items: center;
                    gap: 10px;
                    background: #F7F7F7;
                    @include border-radius(8px);

                    @include dark-theme {
                        background: var(--td-void)73;
                    }
                }
            }

            .author {
                font-size: 14px;
                font-weight: 500;
                color: rgba($heading, $alpha: 0.8);
            }

            .timestamp {
                display: block;
                text-align: right;
                font-size: 14px;
                line-height: 20px;
                letter-spacing: -0.03em;
            }
        }

        .support-meta {
            margin-top: 6px;
            font-size: 14px;
        }
    }

}

.attachment-actions-buttons {
    @include flexbox();
    gap: 14px 14px;
    flex-wrap: wrap;
    margin-top: 15px;
    justify-content: end;

    .add-attachment,
    .add-ticket {
        padding: 10px 12px;
        color: var(--td-white);
        border: none;
        cursor: pointer;
        @include border-radius(4px);
        font-weight: 500;
        font-size: 14px;
        letter-spacing: -0.03em;
        background: var(--td-primary);
        @include border-radius(8px);
    }

    .add-attachment {
        border: 1px solid rgba($heading, $alpha: 0.2);
        background-color: transparent;
        color: #606060;
    }

    .td-btn {
        @media #{$xxs} {
            width: 100%;
        }
    }
}

.support-attachment-form {
    margin-top: 35px;

    .upload-chat-attachments {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 12px 12px;
        margin-bottom: 12px;

        .attachment {
            border-radius: 6.452px;
            border: 1px solid rgba(34, 34, 35, 0.10);
            background: rgba(34, 34, 35, 0.04);
            backdrop-filter: blur(8px);
            border-radius: 6px;
            padding: 6px 8px;
            display: flex;
            align-items: center;
            gap: 8px;

            @include dark-theme {
                background: rgba(255, 255, 255, 0.04);
                border-color: rgba(255, 255, 255, 0.1);
            }

            .screenshot {
                flex: 0 0 auto;
                width: 38px;
                height: 38px;
            }

            .text {
                font-size: 14px;
            }

            .close {
                position: absolute;
                top: -8px;
                inset-inline-end: -6px;
                width: 18px;
                height: 18px;
                background: #EC0707;
                border-radius: 38.5714px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    form {
        textarea {
            border-radius: 12px;
            background: rgba(34, 34, 35, 0.04);
            border: 1px solid rgba($heading, $alpha: 0.16);
            height: 131px;

            @include dark-theme {
                background: rgba(255, 255, 255, 0.04);
                border-color: rgba(255, 255, 255, 0.1);
                color: var(--td-white);
            }
        }
    }
}