@use '../../utils' as *;

/*----------------------------------------*/
/*  Asset management Styles 
/*----------------------------------------*/
.td-asset-management-section {
    @include dark-theme {
        background-color: #0C142B;
    }
}

.asset-management-thumb-wrapper {
    position: relative;

    @media #{$xs,$sm} {
        margin-top: 30px;
    }

    .site-log-box {
        .site-logo {
            width: 186px;
            height: 200px;
            background-color: #F0F4FF;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 50%;
            inset-inline-start: 50%;
            transform: translate(-50%, -50%);
            border-radius: 16px;
            clip-path: polygon(99.903% 0%, 0% 0%, 0% 100%, 82.644% 100%, 99.903% 87.046%, 99.903% 0%);
            z-index: 5;

            &::before {
                position: absolute;
                content: "";
                height: 100%;
                width: 100%;
                top: 0;
                left: 0;
                filter: drop-shadow(0px 0px 70px rgba(191, 150, 255, 0.60));
                z-index: -1;
            }

            @media #{$sm,$md} {
                width: 136px;
                height: 150px;
            }

            @media #{$xs} {
                width: 100px;
                height: 114px;
            }

            @include rtl {
                inset-inline-start: auto;
                inset-inline-end: 50%;
            }

            @include dark-theme {
                background-color: var(--td-void);
            }

            img {
                height: 80px;

                @media #{$xs,$sm} {
                    height: 50px;
                }
            }
        }
    }
}

.asset-management-thumb {
    position: relative;
    z-index: 1;

    &::before {
        position: absolute;
        content: "";
        width: 340px;
        height: 340px;
        background: #8DF4FF;
        opacity: 0.2;
        filter: blur(80px);
        inset-inline-start: 50%;
        transform: translateX(-50%);
        top: 67px;
        z-index: -1;
    }

    img {
        width: 100%;
    }
}

.asset-management-shapes {
    .shape-one {
        position: absolute;
        inset-inline-end: -45px;
        top: -130px;
        z-index: -1;

        img {
            @media #{$xl,$lg,$md} {
                width: 150px;
            }
        }
    }

    .shape-two {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: -1;
        inset-inline-start: 0;

        img {
            @media #{$xl,$lg,$md} {
                width: 150px;
            }
        }
    }
}