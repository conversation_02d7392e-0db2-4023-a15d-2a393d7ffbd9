<?php $__env->startSection('title'); ?>
    <?php echo e(__('Why Work With Us Section')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row justify-content-center">
                    <div class="col-xl-12">
                        <div class="title-content">
                            <h2 class="title"><?php echo $__env->yieldContent('title'); ?></h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php echo $__env->make('backend.page.default.include.__language_bar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>


        <div class="tab-content" id="pills-tabContent">

            <?php $__currentLoopData = $groupData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $data = new Illuminate\Support\Fluent($value);
                ?>

                <div class="tab-pane fade <?php echo e($loop->index == 0 ? 'show active' : ''); ?>" id="<?php echo e($key); ?>"
                    role="tabpanel" aria-labelledby="pills-informations-tab">
                    <div class="site-card">
                        <div class="site-card-header">
                            <h3 class="title"><?php echo e(__('Contents')); ?></h3>
                        </div>
                        <div class="site-card-body">
                            <form action="<?php echo e(route('admin.page.section.section.update')); ?>" method="post"
                                enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="section_code" value="why-work-with-us">
                                <input type="hidden" name="section_locale" value="<?php echo e($key); ?>">
                                <?php if($key == 'en'): ?>
                                    <div class="site-input-groups row">
                                        <label for=""
                                            class="col-sm-3 col-label pt-0"><?php echo e(__('Section Visibility')); ?><i
                                                data-lucide="info" data-bs-toggle="tooltip" title=""
                                                data-bs-original-title="Manage Section Visibility"></i></label>
                                        <div class="col-sm-3">
                                            <div class="site-input-groups">
                                                <div class="switch-field">
                                                    <input type="radio" id="active" name="status"
                                                        <?php if($status): ?> checked <?php endif; ?> value="1" />
                                                    <label for="active"><?php echo e(__('Show')); ?></label>
                                                    <input type="radio" id="deactivate" name="status"
                                                        <?php if(!$status): ?> checked <?php endif; ?> value="0" />
                                                    <label for="deactivate"><?php echo e(__('Hide')); ?></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="site-input-groups row">
                                    <label for="" class="col-sm-3 col-label"><?php echo e(__('Features Title')); ?></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="features_title" class="box-input"
                                            value="<?php echo e($data->features_title); ?>">
                                        <small class="text-muted">
                                            <?php echo e(__('Use this shortcode to highlight words. Example: [[color_text= Your Text Here ]]')); ?>

                                        </small>
                                    </div>
                                </div>
                                <div class="site-input-groups row">
                                    <label for="" class="col-sm-3 col-label"><?php echo e(__('Features Subtitle')); ?></label>
                                    <div class="col-sm-9">
                                        <input type="text" name="features_subtitle" class="box-input"
                                            value="<?php echo e($data->features_subtitle); ?>">
                                    </div>
                                </div>
                                <div class="site-input-groups row">
                                    <label for="" class="col-sm-3 col-label"><?php echo e(__('Button Left')); ?></label>
                                    <div class="col-sm-9">
                                        <div class="form-row">
                                            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 col-12">
                                                <div class="site-input-groups">
                                                    <label for=""
                                                        class="box-input-label"><?php echo e(__('Button Label')); ?></label>
                                                    <input type="text" name="button_label" class="box-input"
                                                        value="<?php echo e($data->button_label); ?>">
                                                </div>
                                            </div>
                                            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 col-12">
                                                <div class="site-input-groups">
                                                    <label for=""
                                                        class="box-input-label"><?php echo e(__('Button URL')); ?></label>
                                                    <div class="site-input-groups">
                                                        <div class="site-input-groups">
                                                            <input type="text" name="button_url" class="box-input"
                                                                value="<?php echo e($data->button_url); ?>">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 col-12">
                                                <div class="site-input-groups">
                                                    <label for=""
                                                        class="box-input-label"><?php echo e(__('Target')); ?></label>
                                                    <div class="site-input-groups">
                                                        <select name="button_target" class="form-select">
                                                            <option <?php if($data->button_target == '_self'): ?> selected <?php endif; ?>
                                                                value="_self"><?php echo e(__('Same Tab')); ?></option>
                                                            <option <?php if($data->button_target == '_blank'): ?> selected <?php endif; ?>
                                                                value="_blank"><?php echo e(__('Open In New Tab')); ?></option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="offset-sm-3 col-sm-9">
                                        <button type="submit"
                                            class="site-btn-sm primary-btn w-100"><?php echo e(__('Save Changes')); ?></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div class="site-card">
            <div class="site-card-header">
                <h3 class="title"><?php echo e(__('Features Contents')); ?></h3>
                <div class="card-header-links">
                    <a href="" class="card-header-link" type="button" data-bs-toggle="modal"
                        data-bs-target="#addNew"><?php echo e(__('Add New')); ?></a>
                </div>
            </div>
            <div class="site-card-body">
                <div class="site-table table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col"><?php echo e(__('Icon')); ?></th>
                                <th scope="col"><?php echo e(__('Title')); ?></th>
                                <th scope="col"><?php echo e(__('Description')); ?></th>
                                <th scope="col"><?php echo e(__('Action')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $landingContent; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <img src="<?php echo e(asset($content->icon)); ?>" alt="<?php echo e($content->title); ?>"
                                            class="img-fluid">
                                    </td>
                                    <td><?php echo e($content->title); ?></td>
                                    <td><?php echo e($content->description); ?></td>
                                    <td>
                                        <button class="round-icon-btn primary-btn editContent" type="button"
                                            data-id="<?php echo e($content->id); ?>">
                                            <i data-lucide="edit-3"></i>
                                        </button>
                                        <button class="round-icon-btn red-btn deleteContent" type="button"
                                            data-id="<?php echo e($content->id); ?>">
                                            <i data-lucide="trash-2"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div> 

    <!-- Modal for Add New  -->
    <?php echo $__env->make('backend.page.default.section.include.__add_new_feature', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Add New End -->

    <!-- Modal for Edit -->
    <?php echo $__env->make('backend.page.default.section.include.__edit', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Edit  End-->

    <!-- Modal for Delete  -->
    <?php echo $__env->make('backend.page.default.section.include.__delete', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for Delete  End-->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        $('.editContent').on('click', function(e) {
            "use strict";
            e.preventDefault();
            var id = $(this).data('id');

            var url = '<?php echo e(route('admin.page.content-edit', ':id')); ?>';
            url = url.replace(':id', id);

            $.ajax({
                url: url,
                type: 'GET',
                success: function(response) {
                    // Handle the response HTML
                    $('#target-element').html(response.html);
                    $('#editContent').modal('show');
                },
                error: function(xhr) {
                    // Handle any errors that occurred during the request
                    console.log(xhr.responseText);
                }
            });
        });

        $('.deleteContent').on('click', function(e) {
            "use strict";
            e.preventDefault();
            var id = $(this).data('id');
            $('#deleteId').val(id);
            $('#deleteContent').modal('show');
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/page/default/section/why-work-with-us.blade.php ENDPATH**/ ?>