<!-- Pricing section start -->
<div class="td-pricing-section p-relative zi-11 <?php if(!Route::is('page')): ?> section_space <?php endif; ?>">
    <div class="container">
       <?php if(Route::is('home')): ?>
       <div class="row justify-content-center">
          <div class="col-xxl-7 col-xl-7 col-lg-6">
            <div class="section-title-wrapper is-white text-center section_title_space">
               <h2 class="section-title has_text_move_anim mb-15"><?php echo highlightColor($data['hero_title']); ?></h2>
               <p class="description has_fade_anim"><?php echo e($data['sub_title']); ?></p>
            </div>
         </div>
      </div>
      <?php endif; ?>
       <div class="row justify-content-center">
          <div class="col-xxl-12">
             <?php echo $__env->make('frontend::user.mining.include.pricing', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
          </div>
       </div>
    </div>
    <div class="pricing-overlay-bg" data-background="<?php echo e(frontendAsset('images/bg/pricing-overlay-bg.png')); ?>"></div>
    <div class="pricing-dot-bg" data-background="<?php echo e(frontendAsset('images/bg/pricing-dot.png')); ?>"></div>
</div>
 <!-- Pricing section end --><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/home/<USER>/__scheme.blade.php ENDPATH**/ ?>