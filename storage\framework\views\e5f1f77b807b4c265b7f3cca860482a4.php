<?php $__env->startSection('title'); ?>
    <?php echo e(__('Coins')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="main-content">
    <div class="page-title">
        <div class="row">
            <div class="col">
                <div class="title-content">
                    <h2 class="title"><?php echo e(__('Coins')); ?></h2>
                    <div>
                        <a href="<?php echo e(route('admin.coin.create')); ?>" class="title-btn">
                            <i icon-name="plus-circle"></i>
                            <?php echo e(__('Add New')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xl-12">
            <div class="site-card">
                <div class="site-card-body table-responsive">
                    <div class="site-datatable">
                        <table id="dataTable" class="display data-table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('SL No')); ?></th>
                                    <th><?php echo e(__('Icon')); ?></th>
                                    <th><?php echo e(__('Name')); ?></th>
                                    <th><?php echo e(__('Code')); ?></th>
                                    <th><?php echo e(__('Symbol')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $currencyInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($key + $currencies->firstItem()); ?></td>
                                        <td>
                                            <?php if($currencyInfo->icon !== null): ?>
                                                <img src="<?php echo e(asset($currencyInfo->icon)); ?>"
                                                    alt="<?php echo e($currencyInfo->name); ?>" width="50" height="50">
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($currencyInfo->name); ?></td>
                                        <td><?php echo e($currencyInfo->code); ?></td>
                                        <td><?php echo e($currencyInfo->symbol); ?></td>
                                        <td>
                                            <?php if($currencyInfo->status == App\Enums\CoinStatus::Active->value): ?>
                                                <div class="site-badge success"><?php echo e(__('Active')); ?></div>
                                            <?php else: ?>
                                                <div class="site-badge pending"><?php echo e(__('Inactive')); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('admin.coin.edit', $currencyInfo->id)); ?>"
                                                class="round-icon-btn primary-btn" data-bs-toggle="tooltip"
                                                title="Edit Currency" data-bs-original-title="Edit Currency">
                                                <i data-lucide="edit-3"></i>
                                            </a>
                                            <?php if (isset($component)) { $__componentOriginal0af61fdba84e7b6b9bbef22c43ef9f33 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0af61fdba84e7b6b9bbef22c43ef9f33 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.delete-module-popup','data' => ['module' => 'Coin','deleteRoute' => route('admin.coin.delete', $currencyInfo->id)]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.delete-module-popup'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['module' => 'Coin','delete-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.coin.delete', $currencyInfo->id))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0af61fdba84e7b6b9bbef22c43ef9f33)): ?>
<?php $attributes = $__attributesOriginal0af61fdba84e7b6b9bbef22c43ef9f33; ?>
<?php unset($__attributesOriginal0af61fdba84e7b6b9bbef22c43ef9f33); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0af61fdba84e7b6b9bbef22c43ef9f33)): ?>
<?php $component = $__componentOriginal0af61fdba84e7b6b9bbef22c43ef9f33; ?>
<?php unset($__componentOriginal0af61fdba84e7b6b9bbef22c43ef9f33); ?>
<?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <td colspan="8" class="text-center"><?php echo e(__('No Currency Found!')); ?></td>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/coin/index.blade.php ENDPATH**/ ?>