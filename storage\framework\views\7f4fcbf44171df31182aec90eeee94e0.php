<?php $__env->startSection('title'); ?>
    <?php echo e(__('Create Coin')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="container-fluid">
            <div class="page-title">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col">
                            <div class="title-content">
                                <h2 class="title"><?php echo e(__('Create Coin')); ?></h2>
                                <div>
                                    <a href="<?php echo e(route('admin.coin.index')); ?>" class="title-btn">
                                        <i icon-name="list"></i>
                                        <?php echo e(__('List')); ?>

                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-8">
                    <div class="site-card">
                        <div class="site-card-body">
                            <form action="<?php echo e(route('admin.coin.store')); ?>" class="row" method="POST"
                                enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>
                                <div class="row">
                                    <div class="col-xl-3">
                                        <div class="site-input-groups">
                                            <?php if (isset($component)) { $__componentOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.drag-and-drop','data' => ['required' => true,'name' => 'icon','label' => ''.e(__('Coin Logo:')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.drag-and-drop'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['required' => true,'name' => 'icon','label' => ''.e(__('Coin Logo:')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa)): ?>
<?php $attributes = $__attributesOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa; ?>
<?php unset($__attributesOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa)): ?>
<?php $component = $__componentOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa; ?>
<?php unset($__componentOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa); ?>
<?php endif; ?>
                                            <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="text-danger"><?php echo e($message); ?></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label">
                                            <?php echo e(__('Name')); ?>: 
                                            <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="box-input" name="name" value="<?php echo e(old('name')); ?>">
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-xl-2">
                                    <div class="site-input-groups">
                                        <label class="box-input-label"><?php echo e(__('Symbol')); ?>: 
                                            <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="box-input" name="symbol" value="<?php echo e(old('symbol')); ?>">
                                        <?php $__errorArgs = ['symbol'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-xl-4">
                                    <div class="site-input-groups">
                                        <label class="box-input-label"><?php echo e(__('Conversion Rate')); ?>: 
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group joint-input">
                                            <span class="input-group-text">1 <?php echo e(setting('site_currency', 'global')); ?> = </span>
                                            <input step="0.<?php echo e(str_repeat('0', 7)); ?>1" type="number" class="form-control" name="conversion_rate" value="<?php echo e(cryptoFormat(old('conversion_rate'))); ?>">
                                            <span class="input-group-text coin-code"></span>
                                        </div>
                                        <?php $__errorArgs = ['symbol'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label">
                                            <?php echo e(__('Code:')); ?> <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="box-input" name="code" id="code"
                                            value="<?php echo e(old('code')); ?>">
                                        <?php $__errorArgs = ['code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label">
                                            <?php echo e(__('Status:')); ?> 
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="switch-field same-type">
                                            <input type="radio" id="radio-five" name="status" value="active"
                                                <?php if(old('status') == 'active'): ?> checked <?php endif; ?> checked>
                                            <label for="radio-five"><?php echo e(__('Active')); ?></label>
                                            <input type="radio" id="radio-six" name="status" value="inactive"
                                                <?php if(old('status') == 'inactive'): ?> checked <?php endif; ?>>
                                            <label for="radio-six"><?php echo e(__('Inactive')); ?></label>
                                        </div>
                                        <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-xl-12">
                                    <button type="submit" class="site-btn primary-btn w-100">
                                        <?php echo e(__('Submit')); ?>

                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        $("#code").on('change', function() {
            $('#currency-selected').text(this.value);
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to toggle visibility of the Exchange Charges section
            function toggleExchangeCharge() {
                var isChecked = document.querySelector('input[name="exchange_charge_status"]:checked').value ===
                    '1';
                document.getElementById('exchange-charges-section').style.display = isChecked ? 'block' : 'none';
            }

            // Function to toggle visibility of the Transfer Charges section
            function toggleTransferCharge() {
                var isChecked = document.querySelector('input[name="transfer_charge_status"]:checked').value ===
                    '1';
                document.getElementById('transfer-charges-section').style.display = isChecked ? 'block' : 'none';
            }

            // Attach event listeners to radio buttons
            document.querySelectorAll('input[name="exchange_charge_status"]').forEach(function(element) {
                element.addEventListener('change', toggleExchangeCharge);
            });

            document.querySelectorAll('input[name="transfer_charge_status"]').forEach(function(element) {
                element.addEventListener('change', toggleTransferCharge);
            });
            $(document).on('input', 'input[name="code"]', function() {
                $('.coin-code').text(this.value);
            });

            // Initial check on page load
            toggleExchangeCharge();
            toggleTransferCharge();
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/coin/create.blade.php ENDPATH**/ ?>