{"__meta": {"id": "01JY17NA09GAS4N34XFHPX4X8F", "datetime": "2025-06-18 15:59:25", "utime": **********.962372, "method": "POST", "uri": "/user/plan-purchase/pay", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[15:59:25] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php on line 59", "message_html": null, "is_string": false, "label": "warning", "time": **********.589452, "xdebug_link": null, "collector": "log"}, {"message": "[15:59:25] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php on line 60", "message_html": null, "is_string": false, "label": "warning", "time": **********.589623, "xdebug_link": null, "collector": "log"}, {"message": "[15:59:25] LOG.warning: number_format(): Passing null to parameter #2 ($decimals) of type int is deprecated in E:\\laragon\\www\\orexcoin\\app\\helpers.php on line 764", "message_html": null, "is_string": false, "label": "warning", "time": **********.686983, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.213353, "end": **********.962391, "duration": 0.7490379810333252, "duration_str": "749ms", "measures": [{"label": "Booting", "start": **********.213353, "relative_start": 0, "end": **********.495892, "relative_end": **********.495892, "duration": 0.*****************, "duration_str": "283ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.4959, "relative_start": 0.****************, "end": **********.962393, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "466ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.510767, "relative_start": 0.*****************, "end": **********.512839, "relative_end": **********.512839, "duration": 0.0020720958709716797, "duration_str": "2.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: backend.mail.user-mail-send", "start": **********.617396, "relative_start": 0.*****************, "end": **********.617396, "relative_end": **********.617396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "mail: New Deposit Request of 95 USD", "start": **********.619482, "relative_start": 0.****************, "end": **********.625079, "relative_end": **********.625079, "duration": 0.*****************, "duration_str": "5.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "mail", "group": null}, {"label": "Preparing Response", "start": **********.708619, "relative_start": 0.****************, "end": **********.960807, "relative_end": **********.960807, "duration": 0.25218796730041504, "duration_str": "252ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::user.add_money.success", "start": **********.71816, "relative_start": 0.5048069953918457, "end": **********.71816, "relative_end": **********.71816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.trnx-success", "start": **********.726968, "relative_start": 0.5136151313781738, "end": **********.726968, "relative_end": **********.726968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user", "start": **********.734639, "relative_start": 0.5212860107421875, "end": **********.734639, "relative_end": **********.734639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.style", "start": **********.750264, "relative_start": 0.5369110107421875, "end": **********.750264, "relative_end": **********.750264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::include._notify", "start": **********.761494, "relative_start": 0.5481410026550293, "end": **********.761494, "relative_end": **********.761494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user_header", "start": **********.769916, "relative_start": 0.556563138961792, "end": **********.769916, "relative_end": **********.769916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.theme-lang-switcher", "start": **********.78315, "relative_start": 0.5697970390319824, "end": **********.78315, "relative_end": **********.78315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.user_sidebar", "start": **********.808261, "relative_start": 0.5949079990386963, "end": **********.808261, "relative_end": **********.808261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.theme-logo", "start": **********.815457, "relative_start": 0.6021041870117188, "end": **********.815457, "relative_end": **********.815457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.855839, "relative_start": 0.6424860954284668, "end": **********.855839, "relative_end": **********.855839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.863564, "relative_start": 0.6502110958099365, "end": **********.863564, "relative_end": **********.863564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.870985, "relative_start": 0.6576321125030518, "end": **********.870985, "relative_end": **********.870985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.881413, "relative_start": 0.6680600643157959, "end": **********.881413, "relative_end": **********.881413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.889476, "relative_start": 0.6761231422424316, "end": **********.889476, "relative_end": **********.889476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.900232, "relative_start": 0.6868791580200195, "end": **********.900232, "relative_end": **********.900232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.908103, "relative_start": 0.6947500705718994, "end": **********.908103, "relative_end": **********.908103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.91517, "relative_start": 0.7018170356750488, "end": **********.91517, "relative_end": **********.91517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.926449, "relative_start": 0.7130961418151855, "end": **********.926449, "relative_end": **********.926449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.937478, "relative_start": 0.7241251468658447, "end": **********.937478, "relative_end": **********.937478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::user._include._menu-item", "start": **********.944763, "relative_start": 0.731410026550293, "end": **********.944763, "relative_end": **********.944763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::layouts.script", "start": **********.952708, "relative_start": 0.7393550872802734, "end": **********.952708, "relative_end": **********.952708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: frontend::include._theme_changer", "start": **********.960266, "relative_start": 0.7469131946563721, "end": **********.960266, "relative_end": **********.960266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 40176072, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "backend.mail.user-mail-send", "param_count": null, "params": [], "start": **********.617362, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/mail/user-mail-send.blade.phpbackend.mail.user-mail-send", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fmail%2Fuser-mail-send.blade.php:1", "ajax": false, "filename": "user-mail-send.blade.php", "line": "?"}}, {"name": "frontend::user.add_money.success", "param_count": null, "params": [], "start": **********.718125, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/add_money/success.blade.phpfrontend::user.add_money.success", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2Fadd_money%2Fsuccess.blade.php:1", "ajax": false, "filename": "success.blade.php", "line": "?"}}, {"name": "components.trnx-success", "param_count": null, "params": [], "start": **********.726951, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/components/trnx-success.blade.phpcomponents.trnx-success", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fcomponents%2Ftrnx-success.blade.php:1", "ajax": false, "filename": "trnx-success.blade.php", "line": "?"}}, {"name": "frontend::layouts.user", "param_count": null, "params": [], "start": **********.734618, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/user.blade.phpfrontend::layouts.user", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser.blade.php:1", "ajax": false, "filename": "user.blade.php", "line": "?"}}, {"name": "frontend::layouts.style", "param_count": null, "params": [], "start": **********.750242, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/style.blade.phpfrontend::layouts.style", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fstyle.blade.php:1", "ajax": false, "filename": "style.blade.php", "line": "?"}}, {"name": "frontend::include._notify", "param_count": null, "params": [], "start": **********.761477, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/include/_notify.blade.phpfrontend::include._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Finclude%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}}, {"name": "frontend::layouts.user_header", "param_count": null, "params": [], "start": **********.769899, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.phpfrontend::layouts.user_header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:1", "ajax": false, "filename": "user_header.blade.php", "line": "?"}}, {"name": "components.theme-lang-switcher", "param_count": null, "params": [], "start": **********.783127, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/components/theme-lang-switcher.blade.phpcomponents.theme-lang-switcher", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fcomponents%2Ftheme-lang-switcher.blade.php:1", "ajax": false, "filename": "theme-lang-switcher.blade.php", "line": "?"}}, {"name": "frontend::layouts.user_sidebar", "param_count": null, "params": [], "start": **********.808243, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/user_sidebar.blade.phpfrontend::layouts.user_sidebar", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_sidebar.blade.php:1", "ajax": false, "filename": "user_sidebar.blade.php", "line": "?"}}, {"name": "components.theme-logo", "param_count": null, "params": [], "start": **********.815438, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/components/theme-logo.blade.phpcomponents.theme-logo", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fcomponents%2Ftheme-logo.blade.php:1", "ajax": false, "filename": "theme-logo.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.855818, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.863549, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.87097, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.8814, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.889459, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.900207, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.908087, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.915157, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.926434, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.93746, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::user._include._menu-item", "param_count": null, "params": [], "start": **********.944747, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.phpfrontend::user._include._menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2F_include%2F_menu-item.blade.php:1", "ajax": false, "filename": "_menu-item.blade.php", "line": "?"}}, {"name": "frontend::layouts.script", "param_count": null, "params": [], "start": **********.952687, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/script.blade.phpfrontend::layouts.script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fscript.blade.php:1", "ajax": false, "filename": "script.blade.php", "line": "?"}}, {"name": "frontend::include._theme_changer", "param_count": null, "params": [], "start": **********.96025, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/include/_theme_changer.blade.phpfrontend::include._theme_changer", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Finclude%2F_theme_changer.blade.php:1", "ajax": false, "filename": "_theme_changer.blade.php", "line": "?"}}]}, "queries": {"count": 25, "nb_statements": 23, "nb_visible_statements": 25, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0194, "accumulated_duration_str": "19.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'CtrebT5vaVz21dyXp5JHzZeSJEEk9d72NMpLDox6' limit 1", "type": "query", "params": [], "bindings": ["CtrebT5vaVz21dyXp5JHzZeSJEEk9d72NMpLDox6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.519743, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 3.969}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.5282412, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "orexcoin", "explain": null, "start_percent": 3.969, "width_percent": 4.227}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.531025, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 8.196, "width_percent": 1.804}, {"sql": "select count(*) as aggregate from `schemes` where `id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}], "start": **********.554889, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "orexcoin", "explain": null, "start_percent": 10, "width_percent": 3.814}, {"sql": "select * from `deposit_methods` where `deposit_methods`.`id` = '4' limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 158}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.561318, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:158", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 158}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:158", "ajax": false, "filename": "PlanPurchaseController.php", "line": "158"}, "connection": "orexcoin", "explain": null, "start_percent": 13.814, "width_percent": 2.887}, {"sql": "select * from `schemes` where `schemes`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 92}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 165}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.562683, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:92", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 92}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:92", "ajax": false, "filename": "PlanPurchaseController.php", "line": "92"}, "connection": "orexcoin", "explain": null, "start_percent": 16.701, "width_percent": 1.546}, {"sql": "select * from `portfolios` where `portfolios`.`id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 96}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 165}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.564753, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:96", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:96", "ajax": false, "filename": "PlanPurchaseController.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 18.247, "width_percent": 2.371}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 96}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 165}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.567636, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:96", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:96", "ajax": false, "filename": "PlanPurchaseController.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 20.619, "width_percent": 5.722}, {"sql": "select * from `coins` where `coins`.`code` = 'BTC' limit 1", "type": "query", "params": [], "bindings": ["BTC"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Models/DepositMethod.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Models\\DepositMethod.php", "line": 71}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 174}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.574405, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DepositMethod.php:71", "source": {"index": 22, "namespace": null, "name": "app/Models/DepositMethod.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Models\\DepositMethod.php", "line": 71}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FDepositMethod.php:71", "ajax": false, "filename": "DepositMethod.php", "line": "71"}, "connection": "orexcoin", "explain": null, "start_percent": 26.34, "width_percent": 2.526}, {"sql": "select * from `templates` where `for` = 'Admin' and `code` = 'admin_manual_deposit' limit 1", "type": "query", "params": [], "bindings": ["Admin", "admin_manual_deposit"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NotifyTrait.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php", "line": 20}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 199}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.5857651, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "NotifyTrait.php:20", "source": {"index": 16, "namespace": null, "name": "app/Traits/NotifyTrait.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php", "line": 20}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FTraits%2FNotifyTrait.php:20", "ajax": false, "filename": "NotifyTrait.php", "line": "20"}, "connection": "orexcoin", "explain": null, "start_percent": 28.866, "width_percent": 5.206}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.615678, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:67", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 67}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FProviders%2FViewServiceProvider.php:67", "ajax": false, "filename": "ViewServiceProvider.php", "line": "67"}, "connection": "orexcoin", "explain": null, "start_percent": 34.072, "width_percent": 2.474}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = schema() and table_name = 'notifications' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/NotifyTrait.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php", "line": 100}, {"index": 23, "namespace": null, "name": "app/Traits/NotifyTrait.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php", "line": 34}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 199}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.6264472, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "NotifyTrait.php:100", "source": {"index": 22, "namespace": null, "name": "app/Traits/NotifyTrait.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php", "line": 100}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FTraits%2FNotifyTrait.php:100", "ajax": false, "filename": "NotifyTrait.php", "line": "100"}, "connection": "orexcoin", "explain": null, "start_percent": 36.546, "width_percent": 6.804}, {"sql": "insert into `notifications` (`icon`, `type`, `user_id`, `for`, `title`, `notice`, `action_url`, `updated_at`, `created_at`) values ('dollar-sign', 'admin_manual_deposit', 1, 'admin', 'New Manual Deposit Request', 'New manual deposit request of 95 USD. Please review and approve.', 'https://orexcoin.test/admin/deposit/manual-pending', '2025-06-18 15:59:25', '2025-06-18 15:59:25')", "type": "query", "params": [], "bindings": ["dollar-sign", "admin_manual_deposit", 1, "admin", "New Manual Deposit Request", "New manual deposit request of 95 USD. Please review and approve.", "https://orexcoin.test/admin/deposit/manual-pending", "2025-06-18 15:59:25", "2025-06-18 15:59:25"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/NotifyTrait.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php", "line": 100}, {"index": 22, "namespace": null, "name": "app/Traits/NotifyTrait.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php", "line": 34}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 199}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.628515, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "NotifyTrait.php:100", "source": {"index": 21, "namespace": null, "name": "app/Traits/NotifyTrait.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\NotifyTrait.php", "line": 100}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FTraits%2FNotifyTrait.php:100", "ajax": false, "filename": "NotifyTrait.php", "line": "100"}, "connection": "orexcoin", "explain": null, "start_percent": 43.351, "width_percent": 23.711}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 202}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.647821, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:202", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 202}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:202", "ajax": false, "filename": "PlanPurchaseController.php", "line": "202"}, "connection": "orexcoin", "explain": null, "start_percent": 67.062, "width_percent": 0}, {"sql": "insert into `transactions` (`user_id`, `from_user_id`, `from_model`, `wallet_type`, `description`, `amount`, `type`, `charge`, `final_amount`, `method`, `pay_currency`, `manual_field_data`, `approval_cause`, `pay_amount`, `target_id`, `target_type`, `is_level`, `status`, `scheme_id`, `tnx`, `updated_at`, `created_at`) values (1, null, 'User', '1', 'Payment for Plan Purchase: Basic Plan', 95, 'Manual_Plan_Purchase', '0.00000001', 95, 'Coinbase Bitcoin', 'BTC', '{\\\"TRX ID\\\":\\\"gatewayPayAmount\\\"}', 'none', 0.0009177000000966, null, null, 0, 'Pending', 2, 'TRXIM9YKRNI1L', '2025-06-18 15:59:25', '2025-06-18 15:59:25')", "type": "query", "params": [], "bindings": [1, null, "User", "1", "Payment for Plan Purchase: Basic Plan", 95, "Manual_Plan_Purchase", "0.00000001", 95, "Coinbase Bitcoin", "BTC", "{\"TRX ID\":\"gatewayPayAmount\"}", "none", 0.0009177000000966001, null, null, 0, "Pending", 2, "TRXIM9YKRNI1L", "2025-06-18 15:59:25", "2025-06-18 15:59:25"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Facades/Txn/Txn.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Facades\\Txn\\Txn.php", "line": 45}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 204}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.6497009, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "Txn.php:45", "source": {"index": 15, "namespace": null, "name": "app/Facades/Txn/Txn.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Facades\\Txn\\Txn.php", "line": 45}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FFacades%2FTxn%2FTxn.php:45", "ajax": false, "filename": "Txn.php", "line": "45"}, "connection": "orexcoin", "explain": null, "start_percent": 67.062, "width_percent": 9.485}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 206}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.65808, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:206", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 206}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:206", "ajax": false, "filename": "PlanPurchaseController.php", "line": "206"}, "connection": "orexcoin", "explain": null, "start_percent": 76.546, "width_percent": 0}, {"sql": "select * from `deposit_methods` where `gateway_code` = 'Coinbase Bitcoin' limit 1", "type": "query", "params": [], "bindings": ["Coinbase Bitcoin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 208}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.6584811, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Payment.php:41", "source": {"index": 16, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 41}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FTraits%2FPayment.php:41", "ajax": false, "filename": "Payment.php", "line": "41"}, "connection": "orexcoin", "explain": null, "start_percent": 76.546, "width_percent": 2.32}, {"sql": "select * from `transactions` where `tnx` = 'TRXIM9YKRNI1L' limit 1", "type": "query", "params": [], "bindings": ["TRXIM9YKRNI1L"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Transaction.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Models\\Transaction.php", "line": 123}, {"index": 25, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 64}, {"index": 26, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 49}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 208}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}], "start": **********.659858, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Transaction.php:123", "source": {"index": 16, "namespace": null, "name": "app/Models/Transaction.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Models\\Transaction.php", "line": 123}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FTransaction.php:123", "ajax": false, "filename": "Transaction.php", "line": "123"}, "connection": "orexcoin", "explain": null, "start_percent": 78.866, "width_percent": 3.093}, {"sql": "select * from `user_wallets` where `user_wallets`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 77}, {"index": 22, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 49}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 208}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.661207, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Payment.php:77", "source": {"index": 21, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 77}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FTraits%2FPayment.php:77", "ajax": false, "filename": "Payment.php", "line": "77"}, "connection": "orexcoin", "explain": null, "start_percent": 81.959, "width_percent": 2.113}, {"sql": "select * from `coins` where `coins`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 77}, {"index": 22, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 49}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 208}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 147}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.674178, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Payment.php:77", "source": {"index": 21, "namespace": null, "name": "app/Traits/Payment.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Traits\\Payment.php", "line": 77}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FTraits%2FPayment.php:77", "ajax": false, "filename": "Payment.php", "line": "77"}, "connection": "orexcoin", "explain": null, "start_percent": 84.072, "width_percent": 2.835}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 392}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.73511, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "helpers.php:392", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 392}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:392", "ajax": false, "filename": "helpers.php", "line": "392"}, "connection": "orexcoin", "explain": null, "start_percent": 86.907, "width_percent": 2.474}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 1 and `notifications`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["user", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::layouts.user_header", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 8}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.770562, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "frontend::layouts.user_header:8", "source": {"index": 15, "namespace": "view", "name": "frontend::layouts.user_header", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 8}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:8", "ajax": false, "filename": "user_header.blade.php", "line": "8"}, "connection": "orexcoin", "explain": null, "start_percent": 89.381, "width_percent": 3.866}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'user' and `user_id` = 1 and `read` = 0 and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["user", 1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::layouts.user_header", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.772055, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend::layouts.user_header:13", "source": {"index": 16, "namespace": "view", "name": "frontend::layouts.user_header", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php", "line": 13}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Flayouts%2Fuser_header.blade.php:13", "ajax": false, "filename": "user_header.blade.php", "line": "13"}, "connection": "orexcoin", "explain": null, "start_percent": 93.247, "width_percent": 1.856}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 400}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.788849, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "helpers.php:400", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 400}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:400", "ajax": false, "filename": "helpers.php", "line": "400"}, "connection": "orexcoin", "explain": null, "start_percent": 95.103, "width_percent": 2.526}, {"sql": "select * from `user_navigations` order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}, {"index": 22, "namespace": "view", "name": "frontend::layouts.user", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/layouts/user.blade.php", "line": 24}], "start": **********.799093, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:73", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 73}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FProviders%2FViewServiceProvider.php:73", "ajax": false, "filename": "ViewServiceProvider.php", "line": "73"}, "connection": "orexcoin", "explain": null, "start_percent": 97.629, "width_percent": 2.371}]}, "models": {"data": {"App\\Models\\Notification": {"value": 31, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\UserNavigation": {"value": 11, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUserNavigation.php:1", "ajax": false, "filename": "UserNavigation.php", "line": "?"}}, "App\\Models\\Language": {"value": 4, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\DepositMethod": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FDepositMethod.php:1", "ajax": false, "filename": "DepositMethod.php", "line": "?"}}, "App\\Models\\Coin": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FCoin.php:1", "ajax": false, "filename": "Coin.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Scheme": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FScheme.php:1", "ajax": false, "filename": "Scheme.php", "line": "?"}}, "App\\Models\\Portfolio": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FPortfolio.php:1", "ajax": false, "filename": "Portfolio.php", "line": "?"}}, "App\\Models\\PortfolioFeature": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FPortfolioFeature.php:1", "ajax": false, "filename": "PortfolioFeature.php", "line": "?"}}, "App\\Models\\Template": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FTemplate.php:1", "ajax": false, "filename": "Template.php", "line": "?"}}, "App\\Models\\Transaction": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FTransaction.php:1", "ajax": false, "filename": "Transaction.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}}, "count": 57, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "New Deposit Request of 95 USD", "headers": "From: Tdevs <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: New Deposit Request of 95 USD\r\n", "body": null, "html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>New Manual Deposit Request</title>\n    <link href=\"https://fonts.googleapis.com/css2?family=Bai+Jamjuree :wght@400;700&family=Roboto&display=swap\" rel=\"stylesheet\">\n    <style>\n        /* Google Font */\n        @import url('https://fonts.googleapis.com/css2?family=Bai+Jamjuree:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');\n\n        /* Root Variables & Typography */\n        :root {\n            --font-base: 16px;\n            --line-height-base: 1.6;\n            --font-ratio: 1.25;\n            --color-heading: #222223;\n            --color-primary: #AA8453;\n            --color-body: #47494E;\n            --color-white: #fff;\n            --font-body: \"Roboto\", sans-serif;\n            --font-heading: \"Bai Jamjuree\", sans-serif;\n        }\n\n        /* Reset Styles */\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        /* Base Body Styles */\n        body {\n            font-family: var(--font-body);\n            font-size: var(--font-base);\n            line-height: calc(var(--font-base) * var(--line-height-base));\n            color: var(--color-body);\n            background-color: hsl(0deg 0% 95.28%);\n        }\n\n        /* Typography Styles */\n        h1,\n        h2,\n        h3,\n        h4,\n        h5,\n        h6 {\n            margin: 0 0 1rem 0;\n            color: var(--font-heading);\n            line-height: 1.2;\n            word-break: break-word;\n            font-weight: 700;\n            color: var(--color-heading);\n        }\n\n        /* Headings */\n        h1 {\n            font-size: clamp(1.5rem, 5vw, 1.75rem);\n            line-height: clamp(2rem, 6vw, 2.5rem);\n        }\n\n        h2 {\n            font-size: clamp(1.25rem, 4.5vw, 1.5rem);\n            line-height: clamp(1.8rem, 5.5vw, 2.125rem);\n        }\n\n        h3 {\n            font-size: clamp(1rem, 4vw, 1.25rem);\n            line-height: clamp(1.5rem, 5vw, 1.875rem);\n        }\n\n        h4 {\n            font-size: clamp(0.9375rem, 3.5vw, 1.125rem);\n            line-height: clamp(1.375rem, 4.5vw, 1.625rem);\n        }\n\n        h5 {\n            font-size: clamp(0.875rem, 3vw, 1rem);\n            line-height: clamp(1.25rem, 4vw, 1.5rem);\n        }\n\n        h6 {\n            font-size: clamp(0.8125rem, 2.5vw, 0.875rem);\n            line-height: clamp(1.125rem, 3.5vw, 1.25rem);\n        }\n\n        /* Paragraph */\n        p {\n            font-size: 16px;\n            line-height: 1.5;\n            margin-bottom: 16px;\n            text-align: left;\n        }\n\n        a {\n            word-wrap: break-word;\n            overflow-wrap: break-word;\n            color: #4776E6;\n        }\n\n        a:hover {\n            text-decoration: none;\n        }\n\n        /* Small Text */\n        small {\n            font-size: 14px;\n            line-height: 22px;\n        }\n\n        img {\n            max-width: 100%;\n            object-fit: cover;\n        }\n\n        .email-container {\n            max-width: 640px;\n            background-color: var(--color-white);\n            margin: 15px auto;\n            padding: 0 30px;\n        }\n\n        /* Header */\n        .header {\n            padding: 20px 0px 20px;\n            position: relative;\n        }\n\n        .header img {\n            height: 33px;\n        }\n\n        /* Hero css */\n        .hero {\n            padding: 0px 0px 20px;\n            position: relative;\n        }\n\n        .hero h1 span {\n\n            background: linear-gradient(90deg, #4776E6 50.12%, #8E54E9 92.23%);\n            background-clip: text;\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n        }\n\n        .hero .thumb {\n            margin-bottom: 20px;\n        }\n\n        .hero .thumb img {\n            width: 100%;\n        }\n\n        /* Features list */\n        .features {\n            padding: 0px 0px 30px;\n        }\n\n        ul li {\n            list-style-type: disc;\n            margin-left: 20px;\n            margin-bottom: 14px;\n        }\n\n        ul li:last-child {\n            margin-bottom: 0;\n        }\n\n        .thanks-contents {\n            padding: 0px 0px 10px;\n            position: relative;\n            margin-bottom: 30px;\n        }\n\n        .thanks-contents span {\n            display: block;\n            color: #9A9DA7;\n        }\n\n        .primary-button {\n            padding: 0 26px;\n            position: relative;\n            z-index: 1;\n            transition: all 0.4s ease-in-out;\n            border-radius: 8px;\n            display: inline-flex;\n            align-items: center;\n            justify-content: center;\n            height: 50px;\n            font-size: 14px;\n            font-weight: 700;\n            gap: 8px;\n            background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);\n            clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);\n            border-radius: 2px;\n            color: var(--color-white);\n            text-decoration: none;\n        }\n\n        .btn-inner {\n            margin-top: 30px;\n        }\n\n        /* Footer Styles */\n        .footer {\n            text-align: center;\n            padding: 0px 0px 30px;\n        }\n\n        .footer p {\n            max-width: 100%;\n            margin: 15px auto 0px;\n            text-align: center;\n        }\n\n        /* Footer Links */\n        .footer-links {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-wrap: wrap;\n            gap: 0.75rem 1rem;\n        }\n\n        .footer-links a {\n            display: inline-flex;\n            align-items: center;\n            text-decoration: underline;\n            font-size: 16px;\n            color: #444344;\n        }\n\n        .footer-links a svg:hover {\n            stroke: var(--color-primary);\n        }\n\n        .footer-links a:hover {\n            text-decoration: none;\n        }\n\n        /* Social Icons */\n        .social-icons {\n            display: inline-flex;\n            align-items: center;\n            justify-content: center;\n            gap: 8px;\n            margin: 0 auto;\n            margin-top: 16px;\n            margin-bottom: 16px;\n        }\n\n        .social-icons a {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            width: 2rem;\n            height: 2rem;\n            color: #4a5cff;\n            transition: color 0.3s ease;\n        }\n\n        .social-icons a svg *:hover {\n            color: var(--color-primary);\n        }\n\n        /* responsive css */\n        @media (max-width: 480px) {\n            .email-container {\n                padding: 0 16px;\n            }\n\n            .features-list {\n                grid-template-columns: 1fr;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"email-container\">\n        <!-- Header -->\n        <div class=\"header\">\n            <a href=\"https://orexcoin.test\">\n                <img src=\"https://orexcoin.test/public/global/uploads/global/uploads/settings//RlbDNZs9kEfJ67hfnZx8.png\" alt=\"Logo\">\n            </a>\n        </div>\n\n        <!-- Hero -->\n        <div class=\"hero\">\n            <h1>Welcome to <span>OrexCoin</span></h1>\n            <p>Hello Admin,</p>\n            <p>A new manual deposit request has been submitted.<br /><br />\n                Amount: 95 USD<br />\n                Charge: 0.00000001 USD<br />\n                Wallet: 1<br />\n                Gateway: Coinbase Bitcoin<br />\n                Requested At: 18 Jun, 2025 03:59 PM<br />\n                Total Amount: 95 USD<br /><br />\n                Please review and approve it.</p>\n        </div>\n        \n        <!-- CTA Button -->\n                <div class=\"thanks-contents\">\n            <a href=\"https://orexcoin.test/admin/deposit/manual-pending\" class=\"primary-button\">View Request</a>\n        </div>\n                <div class=\"hero\">\n        <strong>\n            Regards,<br />OrexCoin\n        </strong>\n        </div>\n\n        <!-- Footer -->\n        <div class=\"footer\">\n            <p>This email was sent to <a href=\"mailto:<EMAIL>\"><EMAIL></a>.</p>\n            <div class=\"footer-links\">\n                <a href=\"https://orexcoin.test\">Home</a>\n                <a href=\"https://orexcoin.test/page/terms-conditions\">Terms and Conditions</a>\n                <a href=\"https://orexcoin.test/page/privacy-policy\">Privacy Policy</a>\n            </div>\n            <div class=\"footer-copyright\">\n                &copy; 2025 OrexCoin.\n            </div>\n        </div>\n    </div>\n</body>\n</html>"}]}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://orexcoin.test/user/plan-purchase/pay", "action_name": "user.plan-purchase.pay", "controller_action": "App\\Http\\Controllers\\Frontend\\PlanPurchaseController@planPurchaseNow", "uri": "POST user/plan-purchase/pay", "controller": "App\\Http\\Controllers\\Frontend\\PlanPurchaseController@planPurchaseNow<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:128\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "user/plan-purchase", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:128\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PlanPurchaseController.php:128-150</a>", "middleware": "web, auth:web, 2fa, check_deactivation, verified, check_feature:plan_purchase,kyc_plan_purchase", "duration": "750ms", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-825353917 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-825353917\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1165814410 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gateway</span>\"\n  \"<span class=sf-dump-key>scheme_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>user_wallet</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>gateway_code</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>manual_data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>TRX ID</span>\" => \"<span class=sf-dump-str title=\"16 characters\">gatewayPayAmount</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165814410\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-251425674 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"755 characters\">declinecookies=true; acceptCookies=true; XSRF-TOKEN=eyJpdiI6Im10UU5TZlVNT3YzN054Zy9XcFRhckE9PSIsInZhbHVlIjoiTUt0cVFxaUxoZEROdktYaFVPV2FmdWRHaDhqV0VCVHcwQ0dQamlIZHFXazZkQU5CRWxjVTgwUUxRK3NzRHRpRlZtSVFYUkZ0blNZTHRUN0NTSHlGMUN6Q0trMFRuaWIrV1k5V0NYLzlCdmdhVjNmci9TYWxkQ0RVNFdQTVRKWXAiLCJtYWMiOiJmN2E3Nzg3MDg2ZTM2MzM5ZTE4ODFjNTQ0N2JkNTM1NDUwZDEwMDlkNDQ0MjM4ZjU3MjA4NmMwMGUxZThiMjllIiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6IjJoYVVGdU5TbkdkMUJzcVNNZ2hyWUE9PSIsInZhbHVlIjoicG8vWWJVWFRCUWJyODY1ZFYrTzBLblpQMCs4Tkdubjc5NXh6N004ZlFNUm13K05EU3FONmJXZDdFVmpqU0U3aDZuUm5uY1o5MytFSG9OcERjRElUSTJJd3VlY0xtQmdtUDVDaWRoT3JHaUg5K1ZrOVdWcVJ0bDl1UEZqalhxY2QiLCJtYWMiOiIzYzM5NDAzZjg1ZjE1ZmNjNDkzNjg3NDY3OGVmN2NkYTlmZGQ2OWQzMDI3Njk0MWRjNGQwZjM5MTM4ODUxMGIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://orexcoin.test/user/plan-purchase/checkout/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryAOn7BnFAxAYC6DAj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">https://orexcoin.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">703</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251425674\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2041531144 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>declinecookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>acceptCookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CtrebT5vaVz21dyXp5JHzZeSJEEk9d72NMpLDox6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041531144\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1828233842 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 09:59:25 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828233842\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-741290865 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oJmwGY0DNaCGhZRXIAW8zKUNRu5UhtaWJSvBg3F2</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">https://orexcoin.test/user/mining</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">notify</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRXIM9YKRNI1L</span>\"\n  \"<span class=sf-dump-key>notify</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"\n    \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Your Manual Plan Purchase request is in Pending</span>\"\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Info</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741290865\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://orexcoin.test/user/plan-purchase/pay", "action_name": "user.plan-purchase.pay", "controller_action": "App\\Http\\Controllers\\Frontend\\PlanPurchaseController@planPurchaseNow"}, "badge": null}}