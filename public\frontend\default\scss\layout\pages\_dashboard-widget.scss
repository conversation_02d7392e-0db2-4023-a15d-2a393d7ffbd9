@use '../../utils' as *;

/*----------------------------------------*/
/*  Dashboard widget styles
/*----------------------------------------*/
.dashboard-widget-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 20px;

    @media #{$xs} {
        grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
    }
}

.dashboard-widget-card {
    display: flex;
    align-items: center;
    background: #091628;
    padding: 18px 18px;
    border-radius: 16px;
    gap: 16px;
    position: relative;
    z-index: 1;

    &::before {
        position: absolute;
        content: "";
        height: 100%;
        width: 100%;
        top: 0;
        inset-inline-start: 0;
        border: 1px solid rgba($heading, $alpha: 0.16);
        border-radius: 16px;
        z-index: -1;

        @include dark-theme {
            border-color: rgba($white, $alpha: 0.16);
        }
    }

    @media #{$xs,$xxl} {
        padding: 12px 12px;
    }

    .icon {
        border-radius: 12px;
        border: 1px solid transparent;
        padding: 10px;
        display: flex;
        flex-direction: row;
        gap: 10px;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        width: 50px;
        height: 50px;
        position: relative;
        background-color: #F3E4FF;

        @include dark-theme {
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        img {
            max-height: 30px;
        }

        @media #{$xs} {
            width: 44px;
            height: 44px;

            img {
                width: 26px;
            }
        }
    }

    .contents {
        .card-title {
            font-size: 16px;
            font-weight: 700;

            @include dark-theme {
                color: #999999;
            }

            @media #{$xs} {
                font-size: 14px;
            }
        }

        .card-value {
            font-size: 18px;
            font-weight: 700;
            margin-top: rem(7);
            color: var(--td-heading);

            @media #{$xs} {
                font-size: 16px;
            }
        }
    }

    &:nth-child(1) {
        background: linear-gradient(74.94deg, #FFFFFF 0%, #EEDAFF 99.54%);

        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #341B4A 99.54%);
        }
    }

    &:nth-child(2) {
        background: linear-gradient(75deg, #FFF 0%, #FFE6DA 99.54%);

        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #351F14 99.54%);
        }
    }

    &:nth-child(3) {
        background: linear-gradient(75deg, #FFF 0%, #FFDAEF 99.54%);

        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #341426 99.54%);
            ;
        }
    }

    &:nth-child(4) {

        background: linear-gradient(75deg, #FFF 0%, #FFFDDB 99.54%);

        @include dark-theme {

            background: linear-gradient(75deg, var(--td-void) 0%, #353313 99.54%);

        }
    }

    &:nth-child(5) {
        background: linear-gradient(75deg, #FFF 0%, #E3FFDD 99.54%);

        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #172D12 99.54%);
        }
    }

    &:nth-child(6) {
        background: linear-gradient(75deg, #FFF 0%, #DAFEFF 99.54%);

        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #122D2E 99.54%);
        }
    }

    &:nth-child(7) {
        background: linear-gradient(75deg, #FFF 0%, #DAE8FF 99.54%);

        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #1B2C4A 99.54%);
        }
    }

    &:nth-child(8) {
        background: linear-gradient(75deg, #FFF 0%, #F0D6FF 99.54%);

        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #1B1E4A 99.54%);
        }
    }

    &:nth-child(9) {
        background: linear-gradient(75deg, #FFF 0%, #DAF3FF 99.54%);

        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #1B3A4A 99.54%);
        }
    }

    &:nth-child(10) {
        @include dark-theme {
            background: linear-gradient(75deg, var(--td-void) 0%, #261B4A 99.54%);
        }

        background: linear-gradient(75deg, #FFF 0%, #FFE4D9 99.54%);
    }
}