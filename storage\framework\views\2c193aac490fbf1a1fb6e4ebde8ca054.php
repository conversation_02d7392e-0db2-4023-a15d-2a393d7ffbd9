
<?php $__env->startSection('title'); ?>
    <?php echo e(__('Create Schema')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <div class="title-content">
                            <h2 class="title"><?php echo e(__('Create New Schema')); ?></h2>
                            <a href="<?php echo e(route('admin.schema.index')); ?>" class="title-btn"><i
                                    icon-name="corner-down-left"></i><?php echo e(__('Back')); ?></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid">
            <form enctype="multipart/form-data" action="<?php echo e(route('admin.schema.store')); ?>" method="post">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-md-6">
                        <div class="site-card">
                            <div class="site-card-header">
                                <h3 class="title">
                                    <?php echo e(__('Basic Info')); ?>

                                </h3>
                            </div>
                            <div class="site-card-body">
                                <div class="row">
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <?php if (isset($component)) { $__componentOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.drag-and-drop','data' => ['required' => true,'name' => 'icon','label' => ''.e(__('Coin Logo:')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.drag-and-drop'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['required' => true,'name' => 'icon','label' => ''.e(__('Coin Logo:')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa)): ?>
<?php $attributes = $__attributesOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa; ?>
<?php unset($__attributesOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa)): ?>
<?php $component = $__componentOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa; ?>
<?php unset($__componentOriginaldd6bc1977619a05ab1ed21a5c4d6f4fa); ?>
<?php endif; ?>
                                            <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="text-danger"><?php echo e($message); ?></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Schema Name:')); ?></label>
                                            <input type="text" value="<?php echo e(old('name')); ?>" name="name" class="box-input" placeholder="Mining Plan Name"
                                                required />
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Miner:')); ?></label>
                                            <select name="miner_id" class="form-select" required>
                                                <option value="" selected disabled><?php echo e(__('Select Miner')); ?></option>
                                                <?php $__currentLoopData = $miners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $miner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option <?php if(old('miner_id') == $miner->id): echo 'selected'; endif; ?> data-coin="<?php echo e($miner->coin->symbol); ?>" value="<?php echo e($miner->id); ?>">
                                                        <?php echo e($miner->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Price:')); ?></label>
                                            <div class="input-group joint-input">
                                                <input type="text" name="price" value="<?php echo e(old('price')); ?>"
                                                    oninput="this.value = validateDouble(this.value)" class="form-control"
                                                    required />
                                                <span class="input-group-text"><?php echo e(setting('currency_symbol', 'global')); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Status:')); ?></label>
                                            <div class="switch-field same-type">
                                                <input type="radio" id="radio-five" name="status" <?php if(old('status') == 'active' || !old('status')): echo 'checked'; endif; ?> value="active" />
                                                <label for="radio-five"><?php echo e(__('Active')); ?></label>
                                                <input type="radio" id="radio-six" name="status" <?php if(old('status') == 'inactive'): echo 'checked'; endif; ?> value="inactive" />
                                                <label for="radio-six"><?php echo e(__('Deactivate')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Featured:')); ?></label>
                                            <div class="switch-field same-type">
                                                <input type="radio" id="featured-yes" name="is_featured" value="1" <?php if(old('is_featured') == 1): echo 'checked'; endif; ?> />
                                                <label for="featured-yes"><?php echo e(__('Yes')); ?></label>
                                                <input type="radio" id="featured-no" name="is_featured" value="0" <?php if(old('is_featured') == 0): echo 'checked'; endif; ?> />
                                                <label for="featured-no"><?php echo e(__('No')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Description:')); ?></label>
                                            <textarea name="description" class="form-textarea" rows="5"><?php echo e(old('description')); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="site-card">
                            <div class="site-card-header">
                                <h3 class="title">
                                    <?php echo e(__('Return Details')); ?>

                                </h3>
                            </div>
                            <div class="site-card-body">
                                <div class="row">
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Return Amount Type:')); ?></label>
                                            <div class="switch-field same-type">
                                                <input type="radio" id="return-fixed" name="return_amount_type" value="fixed"
                                                    <?php if(old('return_amount_type') == 'fixed' || !old('return_amount_type')): echo 'checked'; endif; ?> />
                                                <label for="return-fixed"><?php echo e(__('Fixed')); ?></label>
                                                <input type="radio" id="return-min-max" name="return_amount_type"
                                                    value="min_max" <?php if(old('return_amount_type') == 'min_max'): echo 'checked'; endif; ?> />
                                                <label for="return-min-max"><?php echo e(__('Min-Max')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 return-fixed">
                                        <div class="site-input-groups">
                                            <label class="box-input-label <?php echo e(old('return_amount_type','fixed') == 'min_max' ? 'd-none' : ''); ?>" for=""><?php echo e(__('Return Amount:')); ?></label>
                                            <div class="input-group joint-input">
                                                <input type="text" name="return_amount_value" value="<?php echo e(old('return_amount_value')); ?>"
                                                    oninput="this.value = validateDouble(this.value)" class="form-control"
                                                    required />
                                                <span class="input-group-text mining-coin-symbol"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 return-min-max <?php echo e(old('return_amount_type','fixed') == 'fixed' ? 'd-none' : ''); ?>">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Min Return Amount:')); ?></label>
                                            <div class="input-group joint-input">
                                                <input type="number" name="return_min_amount" value="<?php echo e(old('return_min_amount')); ?>"
                                                    oninput="this.value = validateDouble(this.value)" class="form-control" />
                                                <span class="input-group-text mining-coin-symbol"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 return-min-max <?php echo e(old('return_amount_type','fixed') == 'fixed' ? 'd-none' : ''); ?>">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Max Return Amount:')); ?></label>
                                            <div class="input-group joint-input">
                                                <input type="number" name="return_max_amount" value="<?php echo e(old('return_max_amount')); ?>"
                                                    oninput="this.value = validateDouble(this.value)" class="form-control" />
                                                <span class="input-group-text mining-coin-symbol"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Return Period Type:')); ?></label>
                                            <div class="switch-field same-type">
                                                <input <?php if(!old('return_period_type') || old('return_period_type') == 'period'): echo 'checked'; endif; ?> type="radio" id="return-type-period" name="return_period_type"
                                                    value="period" <?php if(old('return_period_type') == 'period'): echo 'checked'; endif; ?> />
                                                <label for="return-type-period"><?php echo e(__('Period')); ?></label>
                                                <input <?php if(old('return_period_type') == 'lifetime'): echo 'checked'; endif; ?> type="radio" id="return-type-lifetime" name="return_period_type"
                                                    value="lifetime" <?php if(old('return_period_type') == 'lifetime'): echo 'checked'; endif; ?> />
                                                <label for="return-type-lifetime"><?php echo e(__('Lifetime')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 return-period">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Return Period:')); ?>

                                                <i
                                            data-bs-toggle="tooltip"
                                            title="<?php echo e(__('Return Period is the time interval in which the return amount will be given to the user. Coming from Plans > Manage Schema > Schedules menu.')); ?>"
                                            data-lucide="info"></i>
                                            </label>
                                            <select name="return_period" class="form-select" required>
                                                <option value="" selected disabled><?php echo e(__('Select Period')); ?></option>
                                                <?php $__currentLoopData = $schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option <?php if(old('return_period') == $schedule->id): echo 'selected'; endif; ?> value="<?php echo e($schedule->id); ?>"><?php echo e($schedule->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 number-period">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Number of Period:')); ?>

                                                <i
                                            data-bs-toggle="tooltip"
                                            title="<?php echo e(__('Number of Period is the total number of times the return amount will be given to the user.')); ?>"
                                            data-lucide="info"></i>
                                            </label>
                                            <div class="input-group joint-input">
                                                <input type="number" name="return_period_max_number" value="<?php echo e(old('return_period_max_number')); ?>"
                                                    onkeypress="return validateNumber(event)" class="form-control"
                                                    placeholder="Total Repeat Time" required />
                                                <span class="input-group-text"><?php echo e(__('Times')); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="site-card">
                                <div class="site-card-header">
                                    <h3 class="title">
                                        <?php echo e(__('Mining Details')); ?>

                                    </h3>
                                </div>
                                <div class="site-card-body row">
                                    <div class="site-input-groups col-xxl-6">
                                        <label class="box-input-label" for=""><?php echo e(__('Speed:')); ?></label>
                                        <div class="position-relative">
                                            <input type="text" name="speed_amount" class="box-input" value="<?php echo e(old('speed_amount')); ?>"
                                                oninput="this.value = validateDouble(this.value)" required />
                                            <div class="prcntcurr">
                                                <select name="speed" class="form-select">
                                                    <?php $__currentLoopData = $speeds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $speed): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option <?php if(old('speed') == $speed): echo 'selected'; endif; ?> value="<?php echo e($speed); ?>"><?php echo e($speed); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="site-input-groups col-xxl-6">
                                        <label class="box-input-label" for=""><?php echo e(__('Maintenance fee:')); ?></label>
                                        <div class="position-relative">
                                            <input type="text" name="maintenance_fee_amount" class="box-input" value="<?php echo e(old('maintenance_fee_amount')); ?>"
                                                oninput="this.value = validateDouble(this.value)" required />
                                            <div class="prcntcurr">
                                                <select name="maintenance_fee_type" class="form-select">
                                                    <option <?php if(old('maintenance_fee_type') == 'percentage'): echo 'selected'; endif; ?> value="percentage">%</option>
                                                    <option <?php if(old('maintenance_fee_type') == 'fixed'): echo 'selected'; endif; ?> value="fixed"><?php echo e(setting('currency_symbol', 'global')); ?>

                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="site-input-groups col-xxl-6">
                                        <label class="box-input-label" for=""><?php echo e(__('Max Mining Amount:')); ?> <i
                                                data-bs-toggle="tooltip"
                                                title="<?php echo e(__('Max Mining Amount is the maximum amount of coin user can mine in this schema. Leave it blank if there is no limit.')); ?>"
                                                data-lucide="info"></i></label>
                                        <div class="input-group joint-input">
                                            <input value="<?php echo e(old('max_mining_amount')); ?>" type="text" name="max_mining_amount"
                                                oninput="this.value = validateDouble(this.value)"
                                                class="form-control" />
                                            <span class="input-group-text mining-coin-symbol"></span>
                                        </div>
                                    </div>
                                    <div class="site-input-groups col-xxl-6">
                                        <label class="box-input-label" for=""><?php echo e(__('Profit Return Holiday:')); ?></label>
                                        <select id="choices-multiple-remove-button" name="holidays[]"
                                            class="form-select" multiple>
                                            <?php $__currentLoopData = $offDaySchedule; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option <?php if(in_array($day, old('holidays', []))): echo 'selected'; endif; ?> value="<?php echo e($day); ?>"><?php echo e($day); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php $__currentLoopData = $holidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $holiday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option <?php if(in_array($holiday->date, old('holidays', []))): echo 'selected'; endif; ?> value="<?php echo e($holiday->date); ?>"><?php echo e($holiday->name); ?>

                                                    (<?php echo e($holiday->date); ?>)
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <div class="row mt-4">
                    
                    <div class="col-md-6">
                        <div class="site-card">
                            <div class="site-card-header">
                                <h3 class="title">
                                    <?php echo e(__('Features')); ?>

                                </h3>
                            </div>
                            <div class="site-card-body">
                                <div class="site-input-groups">
                                    <label class="box-input-label" for=""><?php echo e(__('Features:')); ?></label>
                                    <div class="features-container">
                                        <div class="feature-row mb-3">
                                            <?php $__empty_1 = true; $__currentLoopData = old('features', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <div class="row mt-2">
                                                <div class="col-11">
                                                    <input type="text" name="features[]" class="box-input"
                                                        placeholder="<?php echo e(__('Feature')); ?>" value="<?php echo e($feature); ?>" required />
                                                </div>
                                                <div class="col-1">
                                                    <button type="button"
                                                        class="delete-feature-btn btn btn-danger"><i
                                                            data-lucide="x"></i></button>
                                                </div>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                            <div class="row mt-2">
                                                <div class="col-11">
                                                    <input type="text" name="features[]" class="box-input"
                                                        placeholder="<?php echo e(__('Feature')); ?>" required />
                                                </div>
                                                <div class="col-1">
                                                    <button type="button"
                                                        class="delete-feature-btn btn btn-danger"><i
                                                            data-lucide="x"></i></button>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <button type="button" id="add-feature-btn" class="site-btn-sm primary-btn mt-2">
                                        <i data-lucide="plus"></i> <?php echo e(__('Add Feature')); ?>

                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-xl-12">
                        <button type="submit" class="site-btn-sm primary-btn w-100">
                            <?php echo e(__('Add New Schema')); ?>

                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('backend/css/choices.min.css')); ?>">
    <style>
        .site-input-groups .prcntcurr{
            width: 110px;
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    <script src="<?php echo e(asset('backend/js/choices.min.js')); ?>"></script>
    <script>
        (function ($) {
            'use strict';

            window.onload = ()=>{
                $('[name="miner_id"]').trigger('change');
            }

            console.log($('[name="miner_id"]').find(':selected').data('coin'));

            $(document).on('change', '[name="miner_id"]', function () {
                var coinSymbol = $(this).find(':selected').data('coin');
                $('.mining-coin-symbol').text(coinSymbol);
            });


            $("#return-fixed").on('click', function () {
                $(".return-fixed").removeClass('d-none');
                $(".return-min-max").addClass('d-none');

                $("input[name='return_amount_value']").prop('required', true);
                $("input[name='return_min_amount']").prop('required', false);
                $("input[name='return_max_amount']").prop('required', false);
            });

            $("#return-min-max").on('click', function () {
                $(".return-fixed").addClass('d-none');
                $(".return-min-max").removeClass('d-none');

                $("input[name='return_amount_value']").prop('required', false);
                $("input[name='return_min_amount']").prop('required', true);
                $("input[name='return_max_amount']").prop('required', true);
            });


            $("#return-type-period").on('click', function () {
                $(".return-period").removeClass('d-none');
                $(".number-period").removeClass('d-none');

                $("select[name='return_period']").prop('required', true);
                $("input[name='return_period_max_number']").prop('required', true);
            });

            $("#return-type-lifetime").on('click', function () {
                $(".number-period").addClass('d-none');

                $("input[name='return_period_max_number']").prop('required', false);
            });


            $("#featured-yes").on('click', function () {
                $(".schema-badge").removeClass('d-none');
                $("input[name='badge']").prop('required', true);
            });

            $("#featured-no").on('click', function () {
                $(".schema-badge").addClass('d-none');
                $("input[name='badge']").prop('required', false);
            });


            var multipleCancelButton = new Choices('#choices-multiple-remove-button', {
                removeItemButton: true,
                maxItemCount: 20,
                searchResultLimit: 20,
                renderChoiceLimit: 20
            });

        })(jQuery);

        function validateDouble(value) {
            return value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
        }

        function validateNumber(evt) {
            var e = evt || window.event;
            var key = e.keyCode || e.which;

            if (!e.shiftKey && !e.altKey && !e.ctrlKey &&

                key >= 48 && key <= 57 ||

                key >= 96 && key <= 105 ||

                key == 8 || key == 9 || key == 13 ||

                key == 35 || key == 36 ||

                key == 37 || key == 39 ||

                key == 46 || key == 45) {

                return true;
            } else {

                e.returnValue = false;
                if (e.preventDefault) e.preventDefault();
                return false;
            }
        }

        $("#add-feature-btn").on('click', function () {
            const featureRow = `
                            <div class="feature-row mb-3">
                                <div class="row mt-2">
                                    <div class="col-11">
                                        <input type="text" name="features[]" class="box-input" placeholder="<?php echo e(__('Feature')); ?>" required />
                                    </div>
                                    <div class="col-1">
                                        <button type="button" class="delete-feature-btn btn btn-danger"><i data-lucide="x"></i></button>
                                    </div>
                                </div>
                            </div>
                        `;

            $('.features-container').append(featureRow);
            lucide.createIcons();
        });

        $(document).on('click', '.delete-feature-btn', function () {
            $(this).closest('.feature-row').remove();
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/schema/create.blade.php ENDPATH**/ ?>