<?php

namespace App\Traits;

use App\Enums\TxnStatus;
use App\Facades\Txn\Txn;
use App\Models\DepositMethod;
use App\Models\Transaction;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\URL;
use Payment\Binance\BinanceTxn;
use Payment\Blockchain\BlockchainTxn;
use Payment\BlockIo\BlockIoTxn;
use Payment\Btcpayserver\BtcpayserverTxn;
use Payment\Cashmaal\CashmaalTxn;
use Payment\Coinbase\CoinbaseTxn;
use Payment\Coingate\CoingateTxn;
use Payment\Coinpayments\CoinpaymentsTxn;
use Payment\Coinremitter\CoinremitterTxn;
use Payment\Cryptomus\CryptomusTxn;
use Payment\Flutterwave\FlutterwaveTxn;
use Payment\Instamojo\InstamojoTxn;
use Payment\Mollie\MollieTxn;
use Payment\Monnify\MonnifyTxn;
use Payment\Nowpayments\NowpaymentsTxn;
use Payment\Paymongo\PaymongoTxn;
use Payment\Paypal\PaypalTxn;
use Payment\Paytm\PaytmTxn;
use Payment\Perfectmoney\PerfectmoneyTxn;
use Payment\Razorpay\RazorpayTxn;
use Payment\Securionpay\SecurionpayTxn;
use Payment\Stripe\StripeTxn;
use Payment\Twocheckout\TwocheckoutTxn;

trait Payment
{
    protected function depositAutoGateway($gateway, $txnInfo)
    {
        $txn = $txnInfo->tnx;
        Session::put('deposit_tnx', $txn);
        $gateway = DepositMethod::code($gateway)->first()->gateway->gateway_code ?? 'none';

        $gatewayTxn = self::gatewayMap($gateway, $txnInfo);

        if ($gatewayTxn) {
            return $gatewayTxn->deposit();
        }

        return self::paymentNotify($txn, 'pending');
    }

    protected function withdrawAutoGateway($gatewayCode, $txnInfo)
    {
        $gatewayTxn = self::gatewayMap($gatewayCode, $txnInfo);
        if ($gatewayTxn && config('app.demo') == 0) {
            $gatewayTxn->withdraw();
        }

        return redirect()->route('user.notify');
    }

    protected function paymentNotify($tnx, $status)
    {
        $tnxInfo = Transaction::tnx($tnx);
        $title = '';
        switch ($status) {
            case 'success':
                $title = 'Successfully';
                break;
            case 'pending':
                $title = 'Pending';
                break;
        }

        $status = ucfirst($status);

        $currencyCode = $tnxInfo->wallet_type == 'default' ? setting('site_currency', 'global') : $tnxInfo->wallet->coin->code;

        $notify = [
            'card-header' => 'Your Deposit Process is '.$status,
            'title' => formatAmount($tnxInfo->amount, $tnxInfo->currency).' '.$currencyCode.' Deposit '.$title,
            'action' => route('user.addMoney'),
            'amount' => cryptoFormat($tnxInfo->pay_amount).' '.$currencyCode,
            'final_amount' => formatAmount($tnxInfo->final_amount, $tnxInfo->currency).' '.$currencyCode,
            'tnxID' => $tnxInfo->tnx,
            'charge' => cryptoFormat($tnxInfo->charge).' '.$currencyCode,
            'type' => $tnxInfo->type_text,
            '_type' => $tnxInfo->type,
            'status' => $status,
        ];
        if ($status == 'Pending') {
            notify()->info(__('Your '.$tnxInfo->type_text.' request is in :status', ['status' => $status]));
        } else {
            notify()->success(__('Your '.$tnxInfo->type_text.' request is :status', ['status' => $status]));
        }

        return view('frontend::user.add_money.success', ['notify' => $notify]);
    }

    protected function paymentSuccess($ref, $isRedirect = true)
    {
        $txnInfo = Transaction::tnx($ref);

        if ($txnInfo->status == TxnStatus::Success) {
            return false;
        }

        $txnInfo->update([
            'status' => TxnStatus::Success,
        ]);

        (new Txn)->update($ref, TxnStatus::Success, $txnInfo->user_id);

        if ($isRedirect) {
            return redirect(URL::temporarySignedRoute(
                'status.success',
                now()->addMinutes(2)
            ));
        }

        return null;
    }

    protected function gatewayMap($gateway, $txnInfo)
    {
        $gatewayMap = [
            'paypal' => PaypalTxn::class,
            'stripe' => StripeTxn::class,
            'mollie' => MollieTxn::class,
            'perfectmoney' => PerfectmoneyTxn::class,
            'coinbase' => CoinbaseTxn::class,
            'paystack' => PaytmTxn::class,
            'voguepay' => BinanceTxn::class,
            'flutterwave' => FlutterwaveTxn::class,
            'cryptomus' => CryptomusTxn::class,
            'nowpayments' => NowpaymentsTxn::class,
            'securionpay' => SecurionpayTxn::class,
            'coingate' => CoingateTxn::class,
            'monnify' => MonnifyTxn::class,
            'coinpayments' => CoinpaymentsTxn::class,
            'paymongo' => PaymongoTxn::class,
            'coinremitter' => CoinremitterTxn::class,
            'btcpayserver' => BtcpayserverTxn::class,
            'binance' => BinanceTxn::class,
            'cashmaal' => CashmaalTxn::class,
            'blockio' => BlockIoTxn::class,
            'blockchain' => BlockchainTxn::class,
            'instamojo' => InstamojoTxn::class,
            'paytm' => PaytmTxn::class,
            'razorpay' => RazorpayTxn::class,
            'twocheckout' => TwocheckoutTxn::class,
        ];

        if (array_key_exists($gateway, $gatewayMap)) {
            return app($gatewayMap[$gateway], ['txnInfo' => $txnInfo]);
        }

        return false;
    }
}
