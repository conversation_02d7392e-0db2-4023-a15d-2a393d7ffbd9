@extends('backend.layouts.app')
@section('title')
    {{ __('Create Schema') }}
@endsection
@section('content')
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <div class="title-content">
                            <h2 class="title">{{ __('Create New Schema') }}</h2>
                            <a href="{{ route('admin.schema.index') }}" class="title-btn"><i
                                    icon-name="corner-down-left"></i>{{ __('Back') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid">
            <form enctype="multipart/form-data" action="{{route('admin.schema.store')}}" method="post">
                @csrf
                <div class="row">
                    <div class="col-md-6">
                        <div class="site-card">
                            <div class="site-card-header">
                                <h3 class="title">
                                    {{ __('Basic Info') }}
                                </h3>
                            </div>
                            <div class="site-card-body">
                                <div class="row">
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <x-admin.drag-and-drop required name="icon" label="{{ __('Coin Logo:') }}" />
                                            @error('icon')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Schema Name:') }}</label>
                                            <input type="text" value="{{ old('name') }}" name="name" class="box-input" placeholder="Mining Plan Name"
                                                required />
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Miner:') }}</label>
                                            <select name="miner_id" class="form-select" required>
                                                <option value="" selected disabled>{{ __('Select Miner') }}</option>
                                                @foreach($miners as $miner)
                                                    <option @selected(old('miner_id') == $miner->id) data-coin="{{ $miner->coin->symbol }}" value="{{ $miner->id }}">
                                                        {{ $miner->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Price:') }}</label>
                                            <div class="input-group joint-input">
                                                <input type="text" name="price" value="{{ old('price') }}"
                                                    oninput="this.value = validateDouble(this.value)" class="form-control"
                                                    required />
                                                <span class="input-group-text">{{ setting('currency_symbol', 'global') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Status:') }}</label>
                                            <div class="switch-field same-type">
                                                <input type="radio" id="radio-five" name="status" @checked(old('status') == 'active' || !old('status')) value="active" />
                                                <label for="radio-five">{{ __('Active') }}</label>
                                                <input type="radio" id="radio-six" name="status" @checked(old('status') == 'inactive') value="inactive" />
                                                <label for="radio-six">{{ __('Deactivate') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Featured:') }}</label>
                                            <div class="switch-field same-type">
                                                <input type="radio" id="featured-yes" name="is_featured" value="1" @checked(old('is_featured') == 1) />
                                                <label for="featured-yes">{{ __('Yes') }}</label>
                                                <input type="radio" id="featured-no" name="is_featured" value="0" @checked(old('is_featured') == 0) />
                                                <label for="featured-no">{{ __('No') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Description:') }}</label>
                                            <textarea name="description" class="form-textarea" rows="5">{{ old('description') }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="site-card">
                            <div class="site-card-header">
                                <h3 class="title">
                                    {{ __('Return Details') }}
                                </h3>
                            </div>
                            <div class="site-card-body">
                                <div class="row">
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Return Amount Type:') }}</label>
                                            <div class="switch-field same-type">
                                                <input type="radio" id="return-fixed" name="return_amount_type" value="fixed"
                                                    @checked(old('return_amount_type') == 'fixed' || !old('return_amount_type')) />
                                                <label for="return-fixed">{{ __('Fixed') }}</label>
                                                <input type="radio" id="return-min-max" name="return_amount_type"
                                                    value="min_max" @checked(old('return_amount_type') == 'min_max') />
                                                <label for="return-min-max">{{ __('Min-Max') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 return-fixed">
                                        <div class="site-input-groups">
                                            <label class="box-input-label {{ old('return_amount_type','fixed') == 'min_max' ? 'd-none' : '' }}" for="">{{ __('Return Amount:') }}</label>
                                            <div class="input-group joint-input">
                                                <input type="text" name="return_amount_value" value="{{ old('return_amount_value') }}"
                                                    oninput="this.value = validateDouble(this.value)" class="form-control"
                                                    required />
                                                <span class="input-group-text mining-coin-symbol"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 return-min-max {{ old('return_amount_type','fixed') == 'fixed' ? 'd-none' : '' }}">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Min Return Amount:') }}</label>
                                            <div class="input-group joint-input">
                                                <input type="number" name="return_min_amount" value="{{ old('return_min_amount') }}"
                                                    oninput="this.value = validateDouble(this.value)" class="form-control" />
                                                <span class="input-group-text mining-coin-symbol"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 return-min-max {{ old('return_amount_type','fixed') == 'fixed' ? 'd-none' : '' }}">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Max Return Amount:') }}</label>
                                            <div class="input-group joint-input">
                                                <input type="number" name="return_max_amount" value="{{ old('return_max_amount') }}"
                                                    oninput="this.value = validateDouble(this.value)" class="form-control" />
                                                <span class="input-group-text mining-coin-symbol"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Return Period Type:') }}</label>
                                            <div class="switch-field same-type">
                                                <input @checked(!old('return_period_type') || old('return_period_type') == 'period') type="radio" id="return-type-period" name="return_period_type"
                                                    value="period" @checked(old('return_period_type') == 'period') />
                                                <label for="return-type-period">{{ __('Period') }}</label>
                                                <input @checked(old('return_period_type') == 'lifetime') type="radio" id="return-type-lifetime" name="return_period_type"
                                                    value="lifetime" @checked(old('return_period_type') == 'lifetime') />
                                                <label for="return-type-lifetime">{{ __('Lifetime') }}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 return-period">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Return Period:') }}
                                                <i
                                            data-bs-toggle="tooltip"
                                            title="{{ __('Return Period is the time interval in which the return amount will be given to the user. Coming from Plans > Manage Schema > Schedules menu.') }}"
                                            data-lucide="info"></i>
                                            </label>
                                            <select name="return_period" class="form-select" required>
                                                <option value="" selected disabled>{{ __('Select Period') }}</option>
                                                @foreach($schedules as $schedule)
                                                    <option @selected(old('return_period') == $schedule->id) value="{{ $schedule->id }}">{{ $schedule->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 number-period">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for="">{{ __('Number of Period:') }}
                                                <i
                                            data-bs-toggle="tooltip"
                                            title="{{ __('Number of Period is the total number of times the return amount will be given to the user.') }}"
                                            data-lucide="info"></i>
                                            </label>
                                            <div class="input-group joint-input">
                                                <input type="number" name="return_period_max_number" value="{{ old('return_period_max_number') }}"
                                                    onkeypress="return validateNumber(event)" class="form-control"
                                                    placeholder="Total Repeat Time" required />
                                                <span class="input-group-text">{{ __('Times') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="site-card">
                                <div class="site-card-header">
                                    <h3 class="title">
                                        {{ __('Mining Details') }}
                                    </h3>
                                </div>
                                <div class="site-card-body row">
                                    <div class="site-input-groups col-xxl-6">
                                        <label class="box-input-label" for="">{{ __('Speed:') }}</label>
                                        <div class="position-relative">
                                            <input type="text" name="speed_amount" class="box-input" value="{{ old('speed_amount') }}"
                                                oninput="this.value = validateDouble(this.value)" required />
                                            <div class="prcntcurr">
                                                <select name="speed" class="form-select">
                                                    @foreach($speeds as $speed)
                                                        <option @selected(old('speed') == $speed) value="{{ $speed }}">{{ $speed }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="site-input-groups col-xxl-6">
                                        <label class="box-input-label" for="">{{ __('Maintenance fee:') }}</label>
                                        <div class="position-relative">
                                            <input type="text" name="maintenance_fee_amount" class="box-input" value="{{ old('maintenance_fee_amount') }}"
                                                oninput="this.value = validateDouble(this.value)" required />
                                            <div class="prcntcurr">
                                                <select name="maintenance_fee_type" class="form-select">
                                                    <option @selected(old('maintenance_fee_type') == 'percentage') value="percentage">%</option>
                                                    <option @selected(old('maintenance_fee_type') == 'fixed') value="fixed">{{ setting('currency_symbol', 'global') }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="site-input-groups col-xxl-6">
                                        <label class="box-input-label" for="">{{ __('Max Mining Amount:') }} <i
                                                data-bs-toggle="tooltip"
                                                title="{{ __('Max Mining Amount is the maximum amount of coin user can mine in this schema. Leave it blank if there is no limit.') }}"
                                                data-lucide="info"></i></label>
                                        <div class="input-group joint-input">
                                            <input value="{{ old('max_mining_amount') }}" type="text" name="max_mining_amount"
                                                oninput="this.value = validateDouble(this.value)"
                                                class="form-control" />
                                            <span class="input-group-text mining-coin-symbol"></span>
                                        </div>
                                    </div>
                                    <div class="site-input-groups col-xxl-6">
                                        <label class="box-input-label" for="">{{ __('Profit Return Holiday:') }}</label>
                                        <select id="choices-multiple-remove-button" name="holidays[]"
                                            class="form-select" multiple>
                                            @foreach ($offDaySchedule as $day)
                                                <option @selected(in_array($day, old('holidays', []))) value="{{ $day }}">{{ $day }}</option>
                                            @endforeach
                                            @foreach($holidays as $holiday)
                                                <option @selected(in_array($holiday->date, old('holidays', []))) value="{{ $holiday->date }}">{{ $holiday->name }}
                                                    ({{ $holiday->date }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
                
                <div class="row mt-4">
                    
                    <div class="col-md-6">
                        <div class="site-card">
                            <div class="site-card-header">
                                <h3 class="title">
                                    {{ __('Features') }}
                                </h3>
                            </div>
                            <div class="site-card-body">
                                <div class="site-input-groups">
                                    <label class="box-input-label" for="">{{ __('Features:') }}</label>
                                    <div class="features-container">
                                        <div class="feature-row mb-3">
                                            @forelse (old('features', []) as $feature)
                                            <div class="row mt-2">
                                                <div class="col-11">
                                                    <input type="text" name="features[]" class="box-input"
                                                        placeholder="{{ __('Feature') }}" value="{{ $feature }}" required />
                                                </div>
                                                <div class="col-1">
                                                    <button type="button"
                                                        class="delete-feature-btn btn btn-danger"><i
                                                            data-lucide="x"></i></button>
                                                </div>
                                            </div>
                                            @empty
                                            <div class="row mt-2">
                                                <div class="col-11">
                                                    <input type="text" name="features[]" class="box-input"
                                                        placeholder="{{ __('Feature') }}" required />
                                                </div>
                                                <div class="col-1">
                                                    <button type="button"
                                                        class="delete-feature-btn btn btn-danger"><i
                                                            data-lucide="x"></i></button>
                                                </div>
                                            </div>
                                            @endforelse
                                        </div>
                                    </div>
                                    <button type="button" id="add-feature-btn" class="site-btn-sm primary-btn mt-2">
                                        <i data-lucide="plus"></i> {{ __('Add Feature') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-xl-12">
                        <button type="submit" class="site-btn-sm primary-btn w-100">
                            {{ __('Add New Schema') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
@section('style')
    <link rel="stylesheet" href="{{ asset('backend/css/choices.min.css') }}">
    <style>
        .site-input-groups .prcntcurr{
            width: 110px;
        }
    </style>
@endsection
@section('script')
    <script src="{{ asset('backend/js/choices.min.js') }}"></script>
    <script>
        (function ($) {
            'use strict';

            window.onload = ()=>{
                $('[name="miner_id"]').trigger('change');
            }

            console.log($('[name="miner_id"]').find(':selected').data('coin'));

            $(document).on('change', '[name="miner_id"]', function () {
                var coinSymbol = $(this).find(':selected').data('coin');
                $('.mining-coin-symbol').text(coinSymbol);
            });


            $("#return-fixed").on('click', function () {
                $(".return-fixed").removeClass('d-none');
                $(".return-min-max").addClass('d-none');

                $("input[name='return_amount_value']").prop('required', true);
                $("input[name='return_min_amount']").prop('required', false);
                $("input[name='return_max_amount']").prop('required', false);
            });

            $("#return-min-max").on('click', function () {
                $(".return-fixed").addClass('d-none');
                $(".return-min-max").removeClass('d-none');

                $("input[name='return_amount_value']").prop('required', false);
                $("input[name='return_min_amount']").prop('required', true);
                $("input[name='return_max_amount']").prop('required', true);
            });


            $("#return-type-period").on('click', function () {
                $(".return-period").removeClass('d-none');
                $(".number-period").removeClass('d-none');

                $("select[name='return_period']").prop('required', true);
                $("input[name='return_period_max_number']").prop('required', true);
            });

            $("#return-type-lifetime").on('click', function () {
                $(".number-period").addClass('d-none');

                $("input[name='return_period_max_number']").prop('required', false);
            });


            $("#featured-yes").on('click', function () {
                $(".schema-badge").removeClass('d-none');
                $("input[name='badge']").prop('required', true);
            });

            $("#featured-no").on('click', function () {
                $(".schema-badge").addClass('d-none');
                $("input[name='badge']").prop('required', false);
            });


            var multipleCancelButton = new Choices('#choices-multiple-remove-button', {
                removeItemButton: true,
                maxItemCount: 20,
                searchResultLimit: 20,
                renderChoiceLimit: 20
            });

        })(jQuery);

        function validateDouble(value) {
            return value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
        }

        function validateNumber(evt) {
            var e = evt || window.event;
            var key = e.keyCode || e.which;

            if (!e.shiftKey && !e.altKey && !e.ctrlKey &&

                key >= 48 && key <= 57 ||

                key >= 96 && key <= 105 ||

                key == 8 || key == 9 || key == 13 ||

                key == 35 || key == 36 ||

                key == 37 || key == 39 ||

                key == 46 || key == 45) {

                return true;
            } else {

                e.returnValue = false;
                if (e.preventDefault) e.preventDefault();
                return false;
            }
        }

        $("#add-feature-btn").on('click', function () {
            const featureRow = `
                            <div class="feature-row mb-3">
                                <div class="row mt-2">
                                    <div class="col-11">
                                        <input type="text" name="features[]" class="box-input" placeholder="{{ __('Feature') }}" required />
                                    </div>
                                    <div class="col-1">
                                        <button type="button" class="delete-feature-btn btn btn-danger"><i data-lucide="x"></i></button>
                                    </div>
                                </div>
                            </div>
                        `;

            $('.features-container').append(featureRow);
            lucide.createIcons();
        });

        $(document).on('click', '.delete-feature-btn', function () {
            $(this).closest('.feature-row').remove();
        });
    </script>
@endsection
