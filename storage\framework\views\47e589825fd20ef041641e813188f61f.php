<h3 class="title"><?php echo e(__('Edit Content')); ?></h3>

<?php echo $__env->make('backend.page.default.include.__language_bar',['editMode' => true], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<div class="tab-content" id="pills-tabContent">
    <?php $__currentLoopData = $groupData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $landingContent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="tab-pane fade <?php echo e($loop->index == 0 ? 'show active' : ''); ?>" id="<?php echo e($key); ?>-render"
            role="tabpanel" aria-labelledby="pills-render-tab">

            <div class="row">
                <div class="col-xl-12">
                    <form action="<?php echo e(route('admin.page.content-update')); ?>" method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="id" value="<?php echo e($landingContent['id']); ?>">
                        <input type="hidden" name="locale" value="<?php echo e($key); ?>">
                        <?php if($key == 'en'): ?>
                            <div class="site-input-groups">
                                <?php if(
                                    $landingContent['type'] == 'agent' ||
                                        $landingContent['type'] == 'merchant' ||
                                        $landingContent['type'] == 'about' ||
                                        $landingContent['type'] == 'features' ||
                                        $landingContent['type'] == 'counter' ||
                                        $landingContent['type'] == 'howitworks' ||
                                        $landingContent['type'] == 'solutions'): ?>
                                    <label class="box-input-label"><?php echo e(__('Icon')); ?></label>
                                    <div class="wrap-custom-file">
                                        <input type="file" name="icon" id="uploadIcon"
                                            accept=".gif, .jpg, .png, .webp" />
                                        <label for="uploadIcon" id="iconPreview" class="file-ok"
                                            style="background-image: url( <?php echo e(asset($landingContent['icon'])); ?> )">
                                            <img class="upload-icon" src="<?php echo e(asset('global/materials/upload.svg')); ?>"
                                                alt="" />
                                            <span><?php echo e(__('Upload')); ?> </span>
                                        </label>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="site-input-groups">
                            <label for="" class="box-input-label">
                                <?php echo e($landingContent['type'] == 'faqs' ? __('Question') : __('Title:')); ?>

                            </label>
                            <input type="text" name="title" value="<?php echo e($landingContent['title']); ?>"
                                class="box-input mb-0 title0" required="" />
                        </div>


                        <div class="site-input-groups mb-0">
                            <label for="" class="box-input-label">
                                <?php if($landingContent['type'] == 'counter'): ?>
                                    <?php echo e(__('Counter')); ?>

                                <?php elseif($landingContent['type'] == 'faqs'): ?>
                                    <?php echo e(__('Answer')); ?>

                                <?php else: ?>
                                    <?php echo e(__('Description')); ?>

                                <?php endif; ?>
                            </label>
                            <textarea name="description" class="form-textarea description" placeholder="Description"><?php echo e($landingContent['description']); ?></textarea>
                        </div>

                        <div class="action-btns">
                            <button type="submit" class="site-btn-sm primary-btn me-2">
                                <i data-lucide="check"></i>
                                <?php echo e(__(' Save Changes')); ?>

                            </button>
                            <a href="#" class="site-btn-sm red-btn" data-bs-dismiss="modal" aria-label="Close">
                                <i data-lucide="x"></i>
                                <?php echo e(__(' Close')); ?>

                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<script>
    $('#uploadPhoto').on('change', function() {
        filePreview($(this), 'label[for=uploadPhoto]');
    });

    $('#uploadIcon').on('change', function() {
        filePreview($(this), 'label[for=uploadIcon]');
    })

    function filePreview(el, target) {
        // Refs
        var file = $(el),
            label = $(target),
            labelText = label.find('span');

        var fileName = file.val().split('\\').pop();
        var tmppath = URL.createObjectURL(file.get(0).files[0]);

        label.css('background-image', 'url(' + tmppath + ')');
        labelText.text(fileName);
    }

    $('#editIconTypes').on('change', function() {
        initIconType();
    });
</script>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/page/default/section/include/__conten_edit_render.blade.php ENDPATH**/ ?>