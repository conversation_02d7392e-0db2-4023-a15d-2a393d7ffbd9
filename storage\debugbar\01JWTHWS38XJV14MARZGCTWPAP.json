{"__meta": {"id": "01JWTHWS38XJV14MARZGCTWPAP", "datetime": "2025-06-03 15:27:53", "utime": **********.704634, "method": "GET", "uri": "/admin/footer-content", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.181544, "end": **********.70465, "duration": 0.5231058597564697, "duration_str": "523ms", "measures": [{"label": "Booting", "start": **********.181544, "relative_start": 0, "end": **********.439405, "relative_end": **********.439405, "duration": 0.****************, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.439413, "relative_start": 0.*****************, "end": **********.704652, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.455039, "relative_start": 0.****************, "end": **********.458083, "relative_end": **********.458083, "duration": 0.0030438899993896484, "duration_str": "3.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.488176, "relative_start": 0.*****************, "end": **********.703139, "relative_end": **********.703139, "duration": 0.*****************, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: backend.page.default.section.footer", "start": **********.499738, "relative_start": 0.****************, "end": **********.499738, "relative_end": **********.499738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.page.default.include.__language_bar", "start": **********.507909, "relative_start": 0.32636499404907227, "end": **********.507909, "relative_end": **********.507909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.page.default.section.include.__add_new_social", "start": **********.522487, "relative_start": 0.3409428596496582, "end": **********.522487, "relative_end": **********.522487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.page.default.section.include.__edit_social", "start": **********.531275, "relative_start": 0.3497309684753418, "end": **********.531275, "relative_end": **********.531275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.page.default.section.include.__delete_social", "start": **********.538439, "relative_start": 0.35689496994018555, "end": **********.538439, "relative_end": **********.538439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.layouts.app", "start": **********.547098, "relative_start": 0.3655538558959961, "end": **********.547098, "relative_end": **********.547098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__head", "start": **********.55431, "relative_start": 0.37276601791381836, "end": **********.55431, "relative_end": **********.55431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: global.admin_notify", "start": **********.572785, "relative_start": 0.39124083518981934, "end": **********.572785, "relative_end": **********.572785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__header", "start": **********.580338, "relative_start": 0.39879393577575684, "end": **********.580338, "relative_end": **********.580338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__notification_data", "start": **********.637904, "relative_start": 0.45635986328125, "end": **********.637904, "relative_end": **********.637904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__side_nav", "start": **********.650192, "relative_start": 0.46864795684814453, "end": **********.650192, "relative_end": **********.650192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__script", "start": **********.694305, "relative_start": 0.5127608776092529, "end": **********.694305, "relative_end": **********.694305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: global.__notification_script", "start": **********.702622, "relative_start": 0.5210778713226318, "end": **********.702622, "relative_end": **********.702622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 35070224, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 13, "nb_templates": 13, "templates": [{"name": "backend.page.default.section.footer", "param_count": null, "params": [], "start": **********.499714, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/section/footer.blade.phpbackend.page.default.section.footer", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Fsection%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "backend.page.default.include.__language_bar", "param_count": null, "params": [], "start": **********.507892, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/include/__language_bar.blade.phpbackend.page.default.include.__language_bar", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Finclude%2F__language_bar.blade.php:1", "ajax": false, "filename": "__language_bar.blade.php", "line": "?"}}, {"name": "backend.page.default.section.include.__add_new_social", "param_count": null, "params": [], "start": **********.52247, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/section/include/__add_new_social.blade.phpbackend.page.default.section.include.__add_new_social", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Fsection%2Finclude%2F__add_new_social.blade.php:1", "ajax": false, "filename": "__add_new_social.blade.php", "line": "?"}}, {"name": "backend.page.default.section.include.__edit_social", "param_count": null, "params": [], "start": **********.531257, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/section/include/__edit_social.blade.phpbackend.page.default.section.include.__edit_social", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Fsection%2Finclude%2F__edit_social.blade.php:1", "ajax": false, "filename": "__edit_social.blade.php", "line": "?"}}, {"name": "backend.page.default.section.include.__delete_social", "param_count": null, "params": [], "start": **********.538424, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/section/include/__delete_social.blade.phpbackend.page.default.section.include.__delete_social", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Fsection%2Finclude%2F__delete_social.blade.php:1", "ajax": false, "filename": "__delete_social.blade.php", "line": "?"}}, {"name": "backend.layouts.app", "param_count": null, "params": [], "start": **********.54707, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/layouts/app.blade.phpbackend.layouts.app", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Flayouts%2Fapp.blade.php:1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "backend.include.__head", "param_count": null, "params": [], "start": **********.554293, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__head.blade.phpbackend.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}}, {"name": "global.admin_notify", "param_count": null, "params": [], "start": **********.572768, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/global/admin_notify.blade.phpglobal.admin_notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fglobal%2Fadmin_notify.blade.php:1", "ajax": false, "filename": "admin_notify.blade.php", "line": "?"}}, {"name": "backend.include.__header", "param_count": null, "params": [], "start": **********.580321, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.phpbackend.include.__header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:1", "ajax": false, "filename": "__header.blade.php", "line": "?"}}, {"name": "backend.include.__notification_data", "param_count": null, "params": [], "start": **********.637888, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__notification_data.blade.phpbackend.include.__notification_data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__notification_data.blade.php:1", "ajax": false, "filename": "__notification_data.blade.php", "line": "?"}}, {"name": "backend.include.__side_nav", "param_count": null, "params": [], "start": **********.650175, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__side_nav.blade.phpbackend.include.__side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__side_nav.blade.php:1", "ajax": false, "filename": "__side_nav.blade.php", "line": "?"}}, {"name": "backend.include.__script", "param_count": null, "params": [], "start": **********.694273, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__script.blade.phpbackend.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}}, {"name": "global.__notification_script", "param_count": null, "params": [], "start": **********.702605, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}}]}, "queries": {"count": 19, "nb_statements": 19, "nb_visible_statements": 19, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00972, "accumulated_duration_str": "9.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'NqrBUu0fQa65JjicUwEYqH1ImIvOdWIb5ExTRXs1' limit 1", "type": "query", "params": [], "bindings": ["NqrBUu0fQa65JjicUwEYqH1ImIvOdWIb5ExTRXs1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.465359, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 5.556}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.4727192, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "orexcoin", "explain": null, "start_percent": 5.556, "width_percent": 4.527}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.474974, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 10.082, "width_percent": 3.395}, {"sql": "select * from `socials` order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 570}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4834518, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PageController.php:570", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 570}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:570", "ajax": false, "filename": "PageController.php", "line": "570"}, "connection": "orexcoin", "explain": null, "start_percent": 13.477, "width_percent": 6.07}, {"sql": "select * from `landing_pages` where `theme` = 'default' and `code` = 'footer'", "type": "query", "params": [], "bindings": ["default", "footer"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 571}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.485088, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PageController.php:571", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 571}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:571", "ajax": false, "filename": "PageController.php", "line": "571"}, "connection": "orexcoin", "explain": null, "start_percent": 19.547, "width_percent": 3.498}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 582}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.486045, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PageController.php:582", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 582}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:582", "ajax": false, "filename": "PageController.php", "line": "582"}, "connection": "orexcoin", "explain": null, "start_percent": 23.045, "width_percent": 2.366}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 64}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.498302, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:64", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 64}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FProviders%2FViewServiceProvider.php:64", "ajax": false, "filename": "ViewServiceProvider.php", "line": "64"}, "connection": "orexcoin", "explain": null, "start_percent": 25.412, "width_percent": 5.453}, {"sql": "select * from `notifications` where `for` = 'admin' and `notifications`.`deleted_at` is null order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.581668, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:2", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 2}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:2", "ajax": false, "filename": "__header.blade.php", "line": "2"}, "connection": "orexcoin", "explain": null, "start_percent": 30.864, "width_percent": 9.568}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.585195, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:2", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 2}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:2", "ajax": false, "filename": "__header.blade.php", "line": "2"}, "connection": "orexcoin", "explain": null, "start_percent": 40.432, "width_percent": 9.362}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'admin' and `read` = 0 and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 3}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.586682, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:3", "source": {"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 3}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:3", "ajax": false, "filename": "__header.blade.php", "line": "3"}, "connection": "orexcoin", "explain": null, "start_percent": 49.794, "width_percent": 3.086}, {"sql": "select * from `notifications` where `for` = 'admin' and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.587428, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:4", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 4}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:4", "ajax": false, "filename": "__header.blade.php", "line": "4"}, "connection": "orexcoin", "explain": null, "start_percent": 52.881, "width_percent": 4.938}, {"sql": "select * from `users` where `users`.`id` in (1, 42, 43, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 4}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.5892842, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:4", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 4}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:4", "ajax": false, "filename": "__header.blade.php", "line": "4"}, "connection": "orexcoin", "explain": null, "start_percent": 57.819, "width_percent": 2.881}, {"sql": "select * from `notifications` where `for` = 'admin' and `notifications`.`deleted_at` is null order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.590231, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:5", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 5}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:5", "ajax": false, "filename": "__header.blade.php", "line": "5"}, "connection": "orexcoin", "explain": null, "start_percent": 60.7, "width_percent": 4.424}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 5}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.5912511, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:5", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 5}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:5", "ajax": false, "filename": "__header.blade.php", "line": "5"}, "connection": "orexcoin", "explain": null, "start_percent": 65.123, "width_percent": 3.189}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'admin' and `read` = 0 and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.592314, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:6", "source": {"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 6}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:6", "ajax": false, "filename": "__header.blade.php", "line": "6"}, "connection": "orexcoin", "explain": null, "start_percent": 68.313, "width_percent": 5.041}, {"sql": "select * from `notifications` where `for` = 'admin' and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.5934129, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:7", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 7}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:7", "ajax": false, "filename": "__header.blade.php", "line": "7"}, "connection": "orexcoin", "explain": null, "start_percent": 73.354, "width_percent": 7.819}, {"sql": "select * from `users` where `users`.`id` in (1, 42, 43, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 7}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.59582, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:7", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 7}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:7", "ajax": false, "filename": "__header.blade.php", "line": "7"}, "connection": "orexcoin", "explain": null, "start_percent": 81.173, "width_percent": 4.218}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 570}], "start": **********.655319, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php:328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "orexcoin", "explain": null, "start_percent": 85.391, "width_percent": 9.774}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.657203, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php:241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "orexcoin", "explain": null, "start_percent": 95.165, "width_percent": 4.835}]}, "models": {"data": {"App\\Models\\Notification": {"value": 192, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\User": {"value": 10, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 4, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Social": {"value": 3, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FSocial.php:1", "ajax": false, "filename": "Social.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FAdmin.php:1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\LandingPage": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLandingPage.php:1", "ajax": false, "filename": "LandingPage.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php:1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 212, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 56, "messages": [{"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2041765363 data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041765363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.66078, "xdebug_link": null}, {"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1078318786 data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078318786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.662017, "xdebug_link": null}, {"message": "[\n  ability => customer-mail-send,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-606701040 data-indent-pad=\"  \"><span class=sf-dump-note>customer-mail-send </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">customer-mail-send</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606701040\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.662793, "xdebug_link": null}, {"message": "[\n  ability => coin-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-985641554 data-indent-pad=\"  \"><span class=sf-dump-note>coin-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">coin-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985641554\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.663735, "xdebug_link": null}, {"message": "[\n  ability => coin-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1882075125 data-indent-pad=\"  \"><span class=sf-dump-note>coin-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">coin-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882075125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.664342, "xdebug_link": null}, {"message": "[\n  ability => miner-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1226402873 data-indent-pad=\"  \"><span class=sf-dump-note>miner-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">miner-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226402873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.664927, "xdebug_link": null}, {"message": "[\n  ability => schedule-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1944632198 data-indent-pad=\"  \"><span class=sf-dump-note>schedule-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">schedule-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944632198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.665555, "xdebug_link": null}, {"message": "[\n  ability => schema-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-74341223 data-indent-pad=\"  \"><span class=sf-dump-note>schema-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">schema-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74341223\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.666081, "xdebug_link": null}, {"message": "[\n  ability => holiday_edit,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-58265464 data-indent-pad=\"  \"><span class=sf-dump-note>holiday_edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">holiday_edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58265464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.666551, "xdebug_link": null}, {"message": "[\n  ability => schema-edit,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-850182950 data-indent-pad=\"  \"><span class=sf-dump-note>schema-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">schema-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850182950\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.666968, "xdebug_link": null}, {"message": "[\n  ability => kyc-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-893281220 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kyc-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-893281220\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.667241, "xdebug_link": null}, {"message": "[\n  ability => kyc-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1579160198 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kyc-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579160198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.66755, "xdebug_link": null}, {"message": "[\n  ability => kyc-form-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1485619542 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-form-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">kyc-form-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485619542\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.667974, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2109134057 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109134057\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.668264, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-9924863 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9924863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.66857, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2099289528 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099289528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.668862, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2013496589 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013496589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.669154, "xdebug_link": null}, {"message": "[\n  ability => transaction-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1569043960 data-indent-pad=\"  \"><span class=sf-dump-note>transaction-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">transaction-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569043960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.669426, "xdebug_link": null}, {"message": "[\n  ability => user-minings-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1027567478 data-indent-pad=\"  \"><span class=sf-dump-note>user-minings-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">user-minings-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027567478\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.66977, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-147770510 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147770510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.670068, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-843641058 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-843641058\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.67033, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-375157195 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375157195\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.670656, "xdebug_link": null}, {"message": "[\n  ability => manual-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1292066315 data-indent-pad=\"  \"><span class=sf-dump-note>manual-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manual-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1292066315\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.671, "xdebug_link": null}, {"message": "[\n  ability => deposit-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1955306372 data-indent-pad=\"  \"><span class=sf-dump-note>deposit-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">deposit-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955306372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.671325, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1679649694 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679649694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.671675, "xdebug_link": null}, {"message": "[\n  ability => withdraw-method-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-381924615 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-method-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">withdraw-method-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381924615\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.671992, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-950405444 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950405444\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.672344, "xdebug_link": null}, {"message": "[\n  ability => withdraw-schedule,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1843321804 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-schedule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">withdraw-schedule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843321804\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.67265, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1853381169 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853381169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.672945, "xdebug_link": null}, {"message": "[\n  ability => referral-create,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-739917518 data-indent-pad=\"  \"><span class=sf-dump-note>referral-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">referral-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739917518\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.673247, "xdebug_link": null}, {"message": "[\n  ability => manage-portfolio,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-34742633 data-indent-pad=\"  \"><span class=sf-dump-note>manage-portfolio </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage-portfolio</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34742633\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.673604, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1774604432 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774604432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.673969, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1975135692 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975135692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.674246, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1101518658 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101518658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.674576, "xdebug_link": null}, {"message": "[\n  ability => email-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1102881791 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">email-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102881791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.674909, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-257946585 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257946585\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.675315, "xdebug_link": null}, {"message": "[\n  ability => language-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-297092264 data-indent-pad=\"  \"><span class=sf-dump-note>language-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">language-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297092264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.675719, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1020153669 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020153669\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.676177, "xdebug_link": null}, {"message": "[\n  ability => plugin-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-281168556 data-indent-pad=\"  \"><span class=sf-dump-note>plugin-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">plugin-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281168556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.676598, "xdebug_link": null}, {"message": "[\n  ability => notification-tune-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1534649934 data-indent-pad=\"  \"><span class=sf-dump-note>notification-tune-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">notification-tune-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534649934\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.677042, "xdebug_link": null}, {"message": "[\n  ability => landing-page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1275957537 data-indent-pad=\"  \"><span class=sf-dump-note>landing-page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">landing-page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275957537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.677439, "xdebug_link": null}, {"message": "[\n  ability => landing-page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2003500607 data-indent-pad=\"  \"><span class=sf-dump-note>landing-page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">landing-page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003500607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.677748, "xdebug_link": null}, {"message": "[\n  ability => custom-css,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-154217996 data-indent-pad=\"  \"><span class=sf-dump-note>custom-css </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">custom-css</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154217996\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.678278, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1205268994 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205268994\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.679044, "xdebug_link": null}, {"message": "[\n  ability => footer-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-956912300 data-indent-pad=\"  \"><span class=sf-dump-note>footer-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">footer-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956912300\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.680291, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-374363036 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374363036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.680792, "xdebug_link": null}, {"message": "[\n  ability => navigation-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-63124205 data-indent-pad=\"  \"><span class=sf-dump-note>navigation-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">navigation-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63124205\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.681639, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1218979963 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218979963\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.682034, "xdebug_link": null}, {"message": "[\n  ability => template-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-180460276 data-indent-pad=\"  \"><span class=sf-dump-note>template-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">template-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180460276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.682376, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-211917359 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211917359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.68272, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-322688536 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322688536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.683043, "xdebug_link": null}, {"message": "[\n  ability => support-ticket-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1688093898 data-indent-pad=\"  \"><span class=sf-dump-note>support-ticket-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">support-ticket-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688093898\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.683381, "xdebug_link": null}, {"message": "[\n  ability => manage-cron-job,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-278650362 data-indent-pad=\"  \"><span class=sf-dump-note>manage-cron-job </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-cron-job</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278650362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.683758, "xdebug_link": null}, {"message": "[\n  ability => manage-cron-job,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-674535387 data-indent-pad=\"  \"><span class=sf-dump-note>manage-cron-job </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-cron-job</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674535387\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.684106, "xdebug_link": null}, {"message": "[\n  ability => clear-cache,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-625469084 data-indent-pad=\"  \"><span class=sf-dump-note>clear-cache </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">clear-cache</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-625469084\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.684464, "xdebug_link": null}, {"message": "[\n  ability => application-details,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-37213238 data-indent-pad=\"  \"><span class=sf-dump-note>application-details </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">application-details</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37213238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.68482, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://orexcoin.test/admin/footer-content", "action_name": "admin.footer-content", "controller_action": "App\\Http\\Controllers\\Backend\\PageController@footerContent", "uri": "GET admin/footer-content", "controller": "App\\Http\\Controllers\\Backend\\PageController@footerContent<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:567\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:567\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/PageController.php:567-593</a>", "middleware": "web, auth:admin", "duration": "524ms", "peak_memory": "36MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1309576075 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1309576075\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-973115128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-973115128\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1790831365 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"734 characters\">acceptCookies=true; XSRF-TOKEN=eyJpdiI6IkxUaW82cUNteGh1S0hDY2hTWThBb0E9PSIsInZhbHVlIjoiUWlIY0U5NHFsK1pSRll5Yk5DZW1DbjZZSE1xbnlrbHRwOVZyeWkwcTllUWtaQUZZeGpiK2NKek0reFhSeThNc3VJcU1VdXNhNm9xME5TUTg3VHB0T0t3VzFWVXhjRk1kMTZHZmRDOHkxT1d2U2JTQ0xaSURUVm85cFFWK1IvbGMiLCJtYWMiOiIzNGU4MTJhOTYwYjA5NGRiZTcyODliZDliNjIxNjdjNzU5OTJiMDFmMDgwMzUwNTNiZDMzNjRhNDExNGE4ZTdjIiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6IkFBZ0NhS1lvV3Z6VzVTSksvTDJmOUE9PSIsInZhbHVlIjoiMXNZd2plNEhoTGVqdkErTSswdzZSU2VERDFGUzE2cmx6UU1zZ1pnOUorOUw1SmJTZ0VTM2NQWFQ1SnpLWVI1eWszSTdCYlRsM0FudmZralJQTEVWK3RBcU9ETzM5OVFwWUFUenIrS2N0L0RTK1d2bEZZcUd3K2szeGltOHRERHgiLCJtYWMiOiJmOTBjMzk0NjVhM2UxNDA0MTI3NzE2NDlhODk1ZWUxZDIzMjkzYmNhMzE5NGJjYzg2MTYzODUxZTVmMTVjODY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://orexcoin.test/admin/footer-content</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790831365\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1120709300 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>acceptCookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vSKNOOwveFCiscf7za5sjtSztXsbm77YHwNmHw3C</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NqrBUu0fQa65JjicUwEYqH1ImIvOdWIb5ExTRXs1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1120709300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2126281559 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 09:27:53 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126281559\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1545811095 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vSKNOOwveFCiscf7za5sjtSztXsbm77YHwNmHw3C</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"36 characters\">https://orexcoin.test/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">notify</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://orexcoin.test/admin/footer-content</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">dark</span>\"\n  \"<span class=sf-dump-key>deposit_tnx</span>\" => \"<span class=sf-dump-str title=\"13 characters\">TRXEXBWQ6XOPO</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>notify</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Social updated successfully</span>\"\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JWTHWRJARF92R7GCEQ1TA4M8</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545811095\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://orexcoin.test/admin/footer-content", "action_name": "admin.footer-content", "controller_action": "App\\Http\\Controllers\\Backend\\PageController@footerContent"}, "badge": null}}